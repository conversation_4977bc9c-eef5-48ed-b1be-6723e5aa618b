traffic_density = 0.3
ambient_vehicle = 0.6
random_vehicle = 0.6
parked_vehicle_density = 0.6
ped_density = 2.0
scenario_ped_density = 1.5

local blocked_zones = {
	['ARMYB'] = true,
	['SLAB'] = true,
}

Citizen.CreateThread(function()
	--SetPedPopulationBudget(100)
	--SetVehiclePopulationBudget(8)
  --SetGlobalPassengerMassMultiplier(0.0) -- Turned off rn due to bug making some cars slower and some faster

	while true do
		Citizen.Wait(0)

		local pos = GetEntityCoords(PlayerPedId())
		local current_zone = GetNameOfZone(pos.x, pos.y, pos.z)

    local ny_zone = false

    if GetResourceState('blrp_yankton') == 'started' then
      pcall(function()
        ny_zone = exports.blrp_yankton:IsInsideNorthYankton()
      end)
    end

		if blocked_zones[current_zone] or ny_zone then
			SetVehicleDensityMultiplierThisFrame(0.0)
			SetRandomVehicleDensityMultiplierThisFrame(0.0)
			SetParkedVehicleDensityMultiplierThisFrame(0.0)
			SetPedDensityMultiplierThisFrame(0.0)
			SetScenarioPedDensityMultiplierThisFrame(0.0, 0.0)
		else
			SetAmbientVehicleRangeMultiplierThisFrame(ambient_vehicle)
			SetVehicleDensityMultiplierThisFrame(traffic_density)
			SetRandomVehicleDensityMultiplierThisFrame(random_vehicle)
			SetParkedVehicleDensityMultiplierThisFrame(parked_vehicle_density)
			SetPedDensityMultiplierThisFrame(ped_density)
			SetScenarioPedDensityMultiplierThisFrame(scenario_ped_density, scenario_ped_density)
		end

		SetPedModelIsSuppressed(`a_m_y_methhead_01`, true)
		SetPedModelIsSuppressed(`g_m_m_mexboss_02`, true)
		SetPedModelIsSuppressed(`a_m_m_hillbilly_01`, true)

    SetVehicleModelIsSuppressed(`towtruck`, true)
    SetVehicleModelIsSuppressed(`towtruck2`, true)
    SetVehicleModelIsSuppressed(`towtruck3`, true)
    SetVehicleModelIsSuppressed(`towtruck4`, true)
	end
end)

Citizen.CreateThread(function()
  SetGlobalPassengerMassMultiplier(0.0)
end)