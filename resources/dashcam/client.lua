local dashcamActive = false
local attachedVehicle = nil
local cameraHandle = nil

Citizen.CreateThread(function()
  while true do
    if dashcamActive then

      if dashcamActive and not IsPedInAnyVehicle(GetPlayerPed(PlayerId()), false) then
        DisableDash()
        dashcamActive = false
      end

      if IsPedInAnyVehicle(GetPlayerPed(PlayerId()), false) and dashcamActive then
        UpdateDashcam()
      end

    end
    Citizen.Wait(1000)
  end
end)

RegisterNetEvent('dashcam:client:toggledashcam')
AddEventHandler('dashcam:client:toggledashcam', function(player, command, args)
  local me = exports.blrp_core:me()

  if dashcamActive then
    me.notify('Dashcam Disabled')
    DisableDash()
  else
    me.notify('Dashcam Enabled')
    EnableDash()
  end

end)

Citizen.CreateThread(function()
  while true do
    if dashcamActive then
      local bonPos = GetWorldPositionOfEntityBone(attachedVehicle, GetEntityBoneIndexByName(attachedVehicle, "windscreen"))
      local vehRot = GetEntityRotation(attachedVehicle, 0)
      SetCamCoord(cameraHandle, bonPos.x, bonPos.y, bonPos.z)
      SetCamRot(cameraHandle, vehRot.x, vehRot.y, vehRot.z, 0)
    end
    Citizen.Wait(0)
  end
end)

function EnableDash()
  attachedVehicle = GetVehiclePedIsIn(GetPlayerPed(PlayerId()), false)
  if DashcamConfig.RestrictVehicles then
    if CheckVehicleRestriction() then
      SetTimecycleModifier("scanline_cam_cheap")
      SetTimecycleModifierStrength(2.2)
      local cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", 1)
      RenderScriptCams(1, 0, 0, 1, 1)
      SetFocusEntity(attachedVehicle)
      cameraHandle = cam
      SendNUIMessage({
        type = "enabledash"
      })
      dashcamActive = true
      TriggerEvent('camera:hideUI', false)
    end
  else
    SetTimecycleModifier("scanline_cam_cheap")
    SetTimecycleModifierStrength(2.2)
    local cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", 1)
    RenderScriptCams(1, 0, 0, 1, 1)
    SetFocusEntity(attachedVehicle)
    cameraHandle = cam
    SendNUIMessage({
      type = "enabledash"
    })
    dashcamActive = true
    TriggerEvent('camera:hideUI', false)
  end
end

function DisableDash()
  ClearTimecycleModifier("scanline_cam_cheap")
  RenderScriptCams(0, 0, 1, 1, 1)
  DestroyCam(cameraHandle, false)
  SetFocusEntity(GetPlayerPed(PlayerId()))
  SendNUIMessage({
    type = "disabledash"
  })
  dashcamActive = false
  TriggerEvent('camera:hideUI', true)
end

function UpdateDashcam()
  local me = exports.blrp_core:me()
  local gameTime = GetGameTimer()
  local year, month, day, hour, minute, second = GetLocalTime()
  local unitNumber = me.get('callsign')
  local unitName = me.get('firstname') .. ' ' .. me.get('lastname')
  local unitSpeed = nil
  local department = nil
  local departmentID = 0

  if me.hasGroup('LSPD_Internal') then
    department = 'Los Santos Police Department'
    departmentID = 1
  elseif me.hasGroup('Sheriff_Internal') then
    department = 'Blaine County Sheriffs Office'
    departmentID = 2
  elseif me.hasGroup('SAHP_Internal') then
    department = 'San Andreas Highway Patrol'
    departmentID = 3
  elseif me.hasGroup('LSFD') then
    department = 'Los Santos Fire Department'
    departmentID = 4
  else
    department = 'State of San Andreas'
    departmentID = 0
  end

  if DashcamConfig.useMPH then
    unitSpeed = GetEntitySpeed(attachedVehicle) * 2.23694
  else
    unitSpeed = GetEntitySpeed(attachedVehicle) * 3.6
  end

  SendNUIMessage({
    type = "updatedash",
    info = {
      gameTime = gameTime,
      clockTime = { year = year, month = month, day = day, hour = hour, minute = minute, second = second },
      unitNumber = unitNumber,
      unitName = unitName,
      unitSpeed = unitSpeed,
      useMPH = DashcamConfig.useMPH,
      department = department,
      departmentID = departmentID
    }
  })
end

function CheckVehicleRestriction()
  if DashcamConfig.RestrictionType == "custom" then
    for a = 1, #DashcamConfig.AllowedVehicles do
      print(GetHashKey(DashcamConfig.AllowedVehicles[a]))
      print(GetEntityModel(attachedVehicle))
      if GetHashKey(DashcamConfig.AllowedVehicles[a]) == GetEntityModel(attachedVehicle) then
        return true
      end
    end
    return false
  elseif DashcamConfig.RestrictionType == "class" then
    if GetVehicleClass(attachedVehicle) == 18 then
      return true
    else
      return false
    end
  else
    return false
  end
end