local callback_id_counter = 0
local callbacks = {}

RegisterServerEvent('cb_vaulthack:server:callback')
AddEventHandler('cb_vaulthack:server:callback', function(callback_id, data)
  local callback = callbacks[callback_id]

  if not callback then
    return
  end

  callbacks[callback_id](data.success)
end)

RegisterServerEvent('cb_vaulthack:server:startGame')
AddEventHandler('cb_vaulthack:server:startGame', function(client_source, callback, options)
  if not client_source then client_source = source end

  local callback_id = callback_id_counter

  callback_id_counter = callback_id_counter + 1

  callbacks[callback_id] = callback

  TriggerClientEvent('cb_vaulthack:client:startGame', client_source, callback_id, options)
end)
