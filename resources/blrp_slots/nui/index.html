<html>
  <head>
    <title>BLRP Slots NUI</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto+Mono">
    <style type="text/css">
      body {
        font-family: "Roboto Mono", sans-serif;
      }

      #container {
        height: 100% !important;

        display: none;
        /* display: flex; */
        justify-content: center;
        align-items: flex-end;
      }

      #wrapper {
        width: 600px;
        height: 120px;

        padding: 10px;

        margin-bottom: 75px;

        border: 5px solid #cec9bd;
        border-radius: 10px;

        box-shadow: 0 0 30px #bdc2ce;

        background: rgb(93,93,93);
        background: linear-gradient(180deg, rgba(93,93,93,1) 0%, rgba(42,42,42,1) 30%, rgba(0,0,0,1) 100%);

        color: white;
      }

      .reel-row {
        display: flex;
        justify-content: space-around;

        width: 100% !important;
      }

      .reel-item {
        display: flex;
      }

      .reel-item span {
        display: inline-flex;
        align-items: center;
        height: 100%;
        font-size: 20px;
        padding-left: 10px;
        width: 10px;
      }

      img {
        height: 30px;
        width: 30px;
      }

      #sub-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      #bet-wrapper {
        display: block;
        position: relative;

        top: -30px;
        left: -30px;
        margin-bottom: -30px;
        padding-left: 10px;

        border: 5px solid #cec9bd;
        border-radius: 10px;

        box-shadow: 0 0 15px #bdc2ce;

        background: rgb(93,93,93);
        background: linear-gradient(180deg, rgba(93,93,93,1) 0%, rgba(42,42,42,1) 30%, rgba(0,0,0,1) 100%);

        color: white;

        width: 75px;
        height: 30px;
      }

      #bet-wrapper span {
        display: inline-flex;
        align-items: center;
        height: 100%;
        font-size: 20px;
      }
    </style>
  </head>

  <body>
    <div id="container">
      <div id="wrapper">
        <div id="bet-wrapper">
          <span>✗</span>
          <span>BET</span>
        </div>

        <div id="sub-wrapper">
          <div class="reel-row">
            <div class="reel-item">
              <img src="images/blank.png" />
              <img src="images/blank.png" />
              <img src="images/01/123.png" data-image="123" />
              <span data-multiplier="1">10</span>
            </div>

            <div class="reel-item">
              <img src="images/blank.png" />
              <img src="images/01/123.png" data-image="123" />
              <img src="images/01/123.png" data-image="123" />
              <span data-multiplier="2">25</span>
            </div>

            <div class="reel-item">
              <img src="images/01/123.png" data-image="123" />
              <img src="images/01/123.png" data-image="123" />
              <img src="images/01/123.png" data-image="123" />
              <span data-multiplier="3">2500</span>
            </div>
          </div>

          <div class="reel-row">
            <div class="reel-item">
              <img src="images/01/4.png" data-image="4" />
              <img src="images/01/4.png" data-image="4" />
              <img src="images/01/4.png" data-image="4" />
              <span data-multiplier="4">125</span>
            </div>

            <div class="reel-item">
              <img src="images/01/5.png" data-image="5" />
              <img src="images/01/5.png" data-image="5" />
              <img src="images/01/5.png" data-image="5" />
              <span data-multiplier="5">250</span>
            </div>

            <div class="reel-item">
              <img src="images/01/6.png" data-image="6" />
              <img src="images/01/6.png" data-image="6" />
              <img src="images/01/6.png" data-image="6" />
              <span data-multiplier="6">375</span>
            </div>
          </div>

          <div class="reel-row">
            <div class="reel-item">
              <img src="images/01/7.png" data-image="7" />
              <img src="images/01/7.png" data-image="7" />
              <img src="images/01/7.png" data-image="7" />
              <span data-multiplier="7">500</span>
            </div>

            <div class="reel-item">
              <img src="images/01/8.png" data-image="8" />
              <img src="images/01/8.png" data-image="8" />
              <img src="images/01/8.png" data-image="8" />
              <span data-multiplier="8">1250</span>
            </div>

            <div class="reel-item">
              <img src="images/01/9.png" data-image="9" />
              <img src="images/01/9.png" data-image="9" />
              <img src="images/01/9.png" data-image="9" />
              <span data-multiplier="9">5000</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script type="text/javascript">
      window.addEventListener("message", (e) => {
        let data = e.data
        let action = data.action

        let container = document.getElementById('container');

        if(action === 'show') {
          if(data.image_set !== null) {
            let images = document.querySelectorAll('[data-image]');

            for(var image in images) {
              if(images.hasOwnProperty(image)) {
                let image_index = images[image].getAttribute('data-image');

                images[image].src = 'images/0' + data.image_set.toString() + '/' + image_index + '.png';
              }
            }
          }

          if(data.multipliers !== null) {
            let spans = document.querySelectorAll('span[data-multiplier]');

            for(var span in spans) {
              if(spans.hasOwnProperty(span)) {
                let multiplier_index = parseInt(spans[span].getAttribute('data-multiplier'));

                if(data.multipliers[multiplier_index]) {
                  spans[span].innerHTML = data.multipliers[multiplier_index].toString();
                }
              }
            }
          }

          container.style.display = 'flex';
        } else if (action === 'hide') {
          container.style.display = 'none';
        }
      })
    </script>
  </body>
</html>
