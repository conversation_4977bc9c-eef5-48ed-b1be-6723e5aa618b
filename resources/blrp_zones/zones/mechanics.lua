addBoxZone('RepairArea305Mechanic', vector3(-1417.71, -445.4099, 39.0608), 25.0, 21.5, 212.0, 34.0, 43.0, {
  repair_mechanic = true,
  disallow_repairing_others_cars = true,
})

addBoxZone('RepairAreaMirrorPark', vector3(1144.843, -782.2399, 60.94617), 25.0, 21.5, 180.0, 56.0, 65.0, {
  repair_mechanic = true
})

addBoxZone('RepairAreaFlywheels', vector3(1769.897, 3331.273, 43.51897), 18.0, 12.0, 120.0, 40.0, 47.0, {
  repair_mechanic = true,
  disallow_repairing_others_cars = true,
})

addPoly<PERSON>one('RepairAreaHayesAuto', {
  vector2(492.54, -1308.84), vector2(500.28, -1321.10), vector2(514.55, -1337.23), vector2(510.03, -1340.84), vector2(482.56, -1340.06),
  vector2(473.45, -1331.57), vector2(478.76, -1329.32), vector2(474.70, -1318.34),
}, 28.0, 40.0, {
  repair_mechanic = true
})

--[[
addPoly<PERSON>one('RepairAreaElginAve', {
  vector2(523.61, -165.90), vector2(540.02, -166.57), vector2(540.74, -171.41), vector2(549.91, -172.43), vector2(549.91, -191.67),
  vector2(539.30, -193.02), vector2(530.85, -192.75), vector2(526.59, -180.16)
}, 50.0, 60.0, {
  repair_mechanic = true
})
]]

addPolyZone('RepairAreaAOD', {
  vector2(1003.49, -125.77), vector2(999.83, -132.59), vector2(994.08, -130.10), vector2(992.35, -133.20), vector2(989.97, -131.92),
  vector2(984.14, -141.81), vector2(956.19, -126.09), vector2(972.71, -111.18),
}, 70.0, 90.0, {
  repair_mechanic = true
})

addPolyZone('RepairAreaPaleto', {
  vector2(-74.48, 6440.79), vector2(-64.52, 6429.87), vector2(-66.83, 6426.72), vector2(-62.52, 6422.62), vector2(-68.97, 6416.19),
  vector2(-84.65, 6430.35)
}, 25.0, 45.0, {
  repair_mechanic = true
})
--[[
addPolyZone('RepairAreaB1', {
  vector2(-3162.857, -190.513), vector2(-3170.104, -202.937), vector2(-3107.655, -239.297), vector2(-3100.934, -226.491),
}, 5.0, 20.0, {
  repair_mechanic = true
}) ]]

--- Factions

addPolyZone('RepairAreaLEOPaleto', {
  vector2(-440.45, 5994.72), vector2(-449.27, 5985.34), vector2(-488.31, 6024.41), vector2(-460.95, 6051.81), vector2(-450.44, 6041.40),
  vector2(-458.63, 6032.70), vector2(-449.44, 6024.08), vector2(-459.46, 6013.98)
}, 25.0, 45.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOMissionRow', {
  vector2(488.13, -1027.95), vector2(467.61, -1030.43), vector2(467.37, -1026.62), vector2(427.59, -1030.90), vector2(427.66, -1010.45),
  vector2(428.61, -1000.28), vector2(423.16, -1000.28), vector2(423.16, -973.04), vector2(463.70, -973.04), vector2(463.74, -1000.32),
  vector2(454.61, -1000.29), vector2(455.57, -1011.49), vector2(466.13, -1012.96), vector2(466.22, -1017.43), vector2(488.32, -1017.46)
}, 20.0, 32.5, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEODavis', {
  vector2(408.16, -1616.06), vector2(387.05, -1641.37), vector2(371.76, -1628.23), vector2(393.06, -1603.46)
}, 20.0, 32.5, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOVespucci', {
  vector2(-1152.201171875, -851.68322753906), vector2(-1128.4072265625, -881.21661376953), vector2(-1123.4541015625, -880.83349609375), vector2(-1099.4538574219, -863.78894042969), vector2(-1079.6577148438, -889.63165283203),
  vector2(-1052.7916259766, -871.81817626953), vector2(-1035.0793457031, -855.88403320312), vector2(-1038.3853759766, -849.55163574219), vector2(-1049.3515625, -840.89965820312), vector2(-1065.7469482422, -851.42590332031), vector2(-1073.2219238281, -843.62432861328),
  vector2(-1076.5775146484, -839.03302001953), vector2(-1070.0627441406, -833.46380615234), vector2(-1079.8015136719, -821.31805419922), vector2(-1093.9560546875, -832.83288574219), vector2(-1080.8671875, -849.15435791016),
  vector2(-1088.2266845703, -862.95050048828), vector2(-1096.5341796875, -853.99725341797), vector2(-1105.8771972656, -857.32202148438), vector2(-1126.3349609375, -872.17413330078), vector2(-1127.1813964844, -870.57305908203),
  vector2(-1106.6306152344, -855.64825439453), vector2(-1121.5903320312, -837.85540771484), vector2(-1117.6392822266, -835.04516601562), vector2(-1109.7524414062, -845.80621337891), vector2(-1089.1838378906, -828.7646484375), vector2(-1101.4417724609, -810.80926513672)
}, 2.0, 20.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOPrison', {
  vector2(1852.36, 2699.72), vector2(1774.58, 2766.22), vector2(1647.21, 2761.09), vector2(1567.56, 2683.7), vector2(1531.14, 2584.81), vector2(1538.1, 2466.13),
  vector2(1659.63, 2390.94), vector2(1762.47, 2406.6), vector2(1828.29, 2475.36), vector2(1846.16, 2568.52),
}, 40.00, 60.00, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOLaMesa', {
  vector2(818.442, -1249.293), vector2(830.424, -1250.748), vector2(840.791, -1254.877), vector2(849.373, -1261.297), vector2(857.431, -1271.588), vector2(861.683, -1284.697),
  vector2(862.734, -1388.883), vector2(816.284, -1383.629),
}, 25.0, 35.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOBeaverBush', {
  vector2(397.355, 777.888), vector2(394.902, 785.110), vector2(378.212, 779.728), vector2(374.372, 784.695), vector2(375.840, 797.196), vector2(372.478, 797.346), 
  vector2(367.879, 782.467), vector2(378.924, 767.902),
}, 175.0, 215.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOVespucciBeach', {
  vector2(-1188.715, -1513.764), vector2(-1171.816, -1501.282), vector2(-1186.157, -1483.535), vector2(-1202.117, -1494.723),
}, 2.0, 15.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEORangerHut', {
  vector2(-1498.731, 4974.293), vector2(-1486.146, 4973.106), vector2(-1482.212, 4995.329), vector2(-1498.963, 4991.073),
}, 50.0, 70.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaSandySO', {
  vector2(1821.24, 3649.17), vector2(1802.10, 3682.18), vector2(1839.43, 3703.69), vector2(1836.54, 3708.47), vector2(1858.35, 3721.83),
  vector2(1880.52, 3683.31)
}, 30.0, 50.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaEMSSandy', {
  vector2(1757.506, 3613.559), vector2(1751.117, 3625.440), vector2(1792.891, 3649.673), vector2(1800.720, 3637.974)
}, 25.0, 45.0, {
  repair_ems = true,
  repair_leo = true
})

addPolyZone('RepairAreaEMSPaleto', {
  vector2(-256.55, 6354.74), vector2(-246.78, 6345.09), vector2(-277.36, 6313.69), vector2(-287.61, 6324.06)
}, 25.0, 45.0, {
  repair_ems = true,
  repair_leo = true
})

addPolyZone('RepairAreaEMSDavis', {
  vector2(205.45, -1684.97), vector2(170.06, -1659.50), vector2(180.12, -1647.13), vector2(193.46, -1655.29), vector2(212.85, -1631.43),
  vector2(224.23, -1640.73), vector2(202.52, -1664.96), vector2(216.75, -1676.21)
}, 25.0, 45.0, {
  repair_ems = true,
  repair_leo = true
})

addPolyZone('RepairAreaEMSPillbox', {
  vector2(333.852, -594.221), vector2(316.708, -587.945), vector2(318.534, -582.688), vector2(311.266, -578.878), vector2(317.444, -562.036),
  vector2(313.308, -559.729), vector2(313.425, -538.374), vector2(351.452, -538.315), vector2(351.300, -549.474)
}, 25.0, 35.0, {
  repair_ems = true,
  repair_leo = true
})

addPolyZone('RepairAreaEMSCapotalblvd', {
  vector2(1192.8380126953, -1486.0053710938),
  vector2(1192.8358154297, -1498.5478515625),
  vector2(1208.6527099609, -1497.6868896484),
  vector2(1208.5129394531, -1486.0609130859)
}, 34.69, 36.0, {
  repair_ems = true,
  repair_leo = true
})

addBoxZone('RepairAreaMidnightClub', vector3(960.45, -1747.39, 21.03), 35.6, 15, 90, 20, 25, {
  repair_mechanic = true,
  repair_thepit = true
})

addBoxZone('RepairAreaMidnightClublower', vector3(983.76, -1814.91, 18.03), 13.4, 11.4, 355, 16.03, 20.03, {
  repair_mechanic = true,
  repair_thepit = true
})

addPolyZone('RepairAreaLEOVinewood', {
  vector2(525.7617, -26.66751), vector2(535.3362, -20.55949), vector2(568.3132, -58.27421), vector2(557.8561, -66.39996),vector2(536.3004, -48.63165)
}, 69.0, 79.0, {
  repair_leo = true,
  repair_ems = true
})

addPolyZone('RepairAreaLEOLawtonJunction', {
  vector2(5985.708, -5217.553), vector2(5945.03, -5213.432), vector2(5947.651, -5185.933), vector2(5987.822, -5191.434)
}, 84.8, 95.0, {
  repair_leo = true
})

addPolyZone('RepairAreaLEOVinewoodMotorPool', {
  vector2(561.5674, 28.76909), vector2(605.7059, 12.70216), vector2(593.7296, -20.18242), vector2(547.9517, -3.473189)
}, 69.0, 79.0, {
  repair_leo = true,
  repair_ems = true
})
