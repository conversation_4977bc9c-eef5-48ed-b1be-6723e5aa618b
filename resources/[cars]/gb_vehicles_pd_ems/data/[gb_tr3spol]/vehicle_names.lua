Citizen.CreateThread(function()
AddTextEntry("POLTR3S", "Police TR3-S")
AddTextEntry("TR3S_MIRROR_1", "Painted Mirrors")
AddTextEntry("TR3S_MIRROR_2", "Carbon Mirrors")
AddTextEntry("TR3S_MIRROR_3", "Painted Mirrors w/ Carbon Base")
AddTextEntry("TR3S_MIRROR_4", "Full Carbon Mirrors")
AddTextEntry("TR3S_AIRVENT_1", "Fender Vent")
AddTextEntry("TR3S_EXHAUST_1", "Exhaust #1")
AddTextEntry("TR3S_EXHAUST_2", "Exhaust #2")
AddTextEntry("TR3S_EXHAUST_3", "Exhaust #3")
AddTextEntry("TR3S_FIN_1", "Fins w/Accent")
AddTextEntry("TR3S_FIN_2", "Carbon Fins w/ Accent")
AddTextEntry("TR3S_BUMPF_1", "Splitter #1 w/ Accent")
AddTextEntry("TR3S_BUMPF_2", "Carbon Splitter #1 w/ Accent")
AddTextEntry("TR3S_BUMPF_3", "Splitter #2 w/ Accent")
AddTextEntry("TR3S_BUMPF_4", "Carbon Splitter #2 w/ Accent")
AddTextEntry("TR3S_BUMPF_5", "Facelift Bumper")
AddTextEntry("TR3S_BUMPF_6", "Facelift Bumper w/ Splitter")
AddTextEntry("TR3S_BUMPF_7", "Facelift Bumper w/ Racing Splitter")
AddTextEntry("TR3S_RDIFF_1", "Diffuser w/ Accent")
AddTextEntry("TR3S_RDIFF_2", "Carbon Diffuser w/ Accent")
AddTextEntry("TR3S_BUMPR_1", "Rear w/ Accent")
AddTextEntry("TR3S_BUMPR_2", "Carbon Rear w/ Accent")
AddTextEntry("TR3S_SKIRT_1", "Skirt w/ Accent")
AddTextEntry("TR3S_SKIRT_2", "Carbon Skirt w/ Accent")
AddTextEntry("TR3S_SPOILER_1", "Fixated Spoiler")
AddTextEntry("TR3S_SPOILER_2", "Lifted Spoiler")
AddTextEntry("TR3S_SPOILER_3", "Sport Spoiler")
AddTextEntry("TR3S_SPOILER_4", "Sport Spoiler w/ Struts")
AddTextEntry("TR3S_SPOILER_5", "Deactivated Spoiler")
AddTextEntry("TR3S_SPOILER_6", "Racing Spoiler")
AddTextEntry("TR3S_SPOILER_7", "Carbon Racing Spoiler")
AddTextEntry("TR3S_SPOILER_8", "Competition Spoiler")
AddTextEntry("TR3S_HOOD_1", "Carbon Hood")
AddTextEntry("TR3S_HOOD_2", "Racing Hood")
AddTextEntry("TR3S_HOOD_3", "Carbon Racing Hood")
AddTextEntry("TR3S_LIV1", "Black Stripes")
AddTextEntry("TR3S_LIV2", "Blue Stripes")
AddTextEntry("TR3S_LIV3", "Green Stripes")
AddTextEntry("TR3S_LIV4", "Orange Stripes")
AddTextEntry("TR3S_LIV5", "Red Stripes")
AddTextEntry("TR3S_LIV6", "White Stripes")
AddTextEntry("TR3S_LIV7", "Racing Heritage")

    AddTextEntry("POLPROTR3S_LIV1", "Los Santos Police Department")
    AddTextEntry("POLPROTR3S_LIV2", "Los Santos International Airport Police Department")
    AddTextEntry("POLPROTR3S_LIV3", "Los Santos County Sheriff's Office")
    AddTextEntry("POLPROTR3S_LIV4", "National Office of Security Enforcement")
    AddTextEntry("POLPROTR3S_LIV5", "Port Authority of Los Santos")
    AddTextEntry("POLPROTR3S_LIV6", "Los Santos County Park Ranger")
    AddTextEntry("POLPROTR3S_LIV7", "San Andreas Highway Patrol")
    AddTextEntry("POLPROTR3S_LIV8", "Bolingbroke Penitentiary State Prison")
    AddTextEntry("POLPROTR3S_LIV9", "Bolingbroke Penitentiary State Prison (Uncolorable)")
    AddTextEntry("POLPROTR3S_LIV10", "Los Santos County Sheriff's Office (Stealth)")
    AddTextEntry("POLPROTR3S_LIV11", "San Andreas Highway Patrol (High Speed Pursuit)")
	
    AddTextEntryByHash(GetHashKey('GBCS_L'), "Left Callsign")
	AddTextEntryByHash(GetHashKey('GBCS_C'), "Middle Callsign")
	AddTextEntryByHash(GetHashKey('GBCS_R'), "Right Callsign")
	
AddTextEntryByHash(GetHashKey('GBPOL_CS_0'), "CALLSIGN 0")
AddTextEntryByHash(GetHashKey('GBPOL_CS_1'), "CALLSIGN 1")
AddTextEntryByHash(GetHashKey('GBPOL_CS_2'), "CALLSIGN 2")
AddTextEntryByHash(GetHashKey('GBPOL_CS_3'), "CALLSIGN 3")
AddTextEntryByHash(GetHashKey('GBPOL_CS_4'), "CALLSIGN 4")
AddTextEntryByHash(GetHashKey('GBPOL_CS_5'), "CALLSIGN 5")
AddTextEntryByHash(GetHashKey('GBPOL_CS_6'), "CALLSIGN 6")
AddTextEntryByHash(GetHashKey('GBPOL_CS_7'), "CALLSIGN 7")
AddTextEntryByHash(GetHashKey('GBPOL_CS_8'), "CALLSIGN 8")
AddTextEntryByHash(GetHashKey('GBPOL_CS_9'), "CALLSIGN 9")
end)