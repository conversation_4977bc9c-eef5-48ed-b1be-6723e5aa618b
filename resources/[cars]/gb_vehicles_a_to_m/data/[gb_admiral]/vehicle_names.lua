Citizen.CreateThread(function()
    AddTextEntry("GBADMIRA<PERSON>", "Admiral")
    AddTextEntry("ADM_LIV1", "Black Pinstripes")
    AddTextEntry("ADM_LIV2", "White Pinstripes")
    AddTextEntry("ADM_LIV3", "Grey Pinstripes")
    AddTextEntry("ADM_LIV4", "Gold Pinstripes")
    AddTextEntry("ADM_LIV5", "Red Elaborate Pinstripes")
    AddTextEntry("ADM_LIV6", "Yellow Elaborate Pinstripes")
    AddTextEntry("ADM_LIV7", "Green Elaborate Pinstripes")
    AddTextEntry("ADM_LIV8", "Blue Elaborate Pinstripes")
    AddTextEntry("ADM_LIV9", "Purple Elaborate Pinstripes")
    AddTextEntry("ADM_LIV10", "Grey Elaborate Pinstripes")
    AddTextEntry("ADM_LIV11", "Cardiaque Brandy")

        AddTextEntry("ADM_BUMF1A", "Painted Bumper")
        AddTextEntry("ADM_BUMF1B", "Painted & Extra Chrome Bumper")
        AddTextEntry("ADM_BUMF1C", "Extra Chrome Bumper")
        AddTextEntry("ADM_BUMF1D", "Gold Trim Bumper")
        AddTextEntry("ADM_BUMF1E", "Painted & Extra Gold Bumper")
        AddTextEntry("ADM_BUMF1F", "Extra Gold Bumper")
        AddTextEntry("ADM_BUMF2A", "Retro Bumper")
        AddTextEntry("ADM_BUMF2B", "Painted Retro Bumper")
        AddTextEntry("ADM_BUMF2C", "Painted & Extra Chrome Retro Bumper")
        AddTextEntry("ADM_BUMF2D", "Extra Chrome Retro Bumper")
        AddTextEntry("ADM_BUMF2E", "Gold Trim Retro Bumper")
        AddTextEntry("ADM_BUMF2F", "Painted & Extra Gold Retro Bumper")
        AddTextEntry("ADM_BUMF2G", "Extra Gold Retro Bumper")

        AddTextEntry("ADM_DKIT1", "Spare Wheel")
        AddTextEntry("ADM_DKIT2", "Gold Trim Spare Wheel")
        AddTextEntry("ADM_BUMR1A", "Painted Bumper")
        AddTextEntry("ADM_BUMR1B", "Painted & Extra Chrome Bumper")
        AddTextEntry("ADM_BUMR1C", "Extra Chrome Bumper")
        AddTextEntry("ADM_BUMR1D", "Gold Trim Bumper")
        AddTextEntry("ADM_BUMR1E", "Painted & Extra Gold Bumper")
        AddTextEntry("ADM_BUMR1F", "Extra Gold Bumper")

        AddTextEntry("ADM_HLIGHTS1", "Gold Headlight Housings")

        AddTextEntry("ADM_SKIRT1A", "Chrome Skirts")

        AddTextEntry("ADM_ANT", "Boomerang Antenna")
        AddTextEntry("ADM_TRUNKR1A", "Trunk Rack")
        AddTextEntry("ADM_TRUNKR1B", "Gold Trunk Rack")
        AddTextEntry("ADM_SPL1A", "Chrome Low Ducktail")
        AddTextEntry("ADM_SPL1B", "Plastic Low Ducktail")
        AddTextEntry("ADM_SPL1C", "Paint Low Ducktail")
        AddTextEntry("ADM_SPL1D", "Gold Low Ducktail")

        AddTextEntry("ADM_GRILL1A", "Painted Grille")
        AddTextEntry("ADM_GRILL1B", "Gold Grille")
        AddTextEntry("ADM_GRILL2A", "Retro Grille")
        AddTextEntry("ADM_GRILL2B", "Painted Retro Grille")
        AddTextEntry("ADM_GRILL2C", "Gold Retro Grille")
        AddTextEntry("ADM_GRILL3A", "Thick Bar Grille")
        AddTextEntry("ADM_GRILL3B", "Painted Thick Bar Grille")
        AddTextEntry("ADM_GRILL3C", "Gold Thick Bar Grille")
        AddTextEntry("ADM_GRILL4A", "Liberty Grille")
        AddTextEntry("ADM_GRILL4B", "Painted Liberty Grille")
        AddTextEntry("ADM_GRILL4C", "Gold Liberty Grille")

        AddTextEntry("ADM_SSTRIP", "Sunstrip")

        AddTextEntry("ADM_WTRIM1", "Gold Window Trim")
        AddTextEntry("ADM_WTRIM2A", "Full Chrome Window Trim")
        AddTextEntry("ADM_WTRIM2B", "Full Gold Window Trim")

        AddTextEntry("ADM_WDEF", "Wind Deflectors")
        AddTextEntry("ADM_RSPL1A", "Painted Roof Spoiler")
        AddTextEntry("ADM_RSPL1B", "Plastic Roof Spoiler")
        AddTextEntry("ADM_RSPL1C", "Chrome Roof Spoiler")
        AddTextEntry("ADM_RSPL1D", "Gold Roof Spoiler")
        AddTextEntry("ADM_VROOF1A", "Leather Vinyl Roof")
        AddTextEntry("ADM_VROOF2A", "Vinyl Roof")
        AddTextEntry("ADM_VROOF3A", "Zebra Pattern Roof")
        AddTextEntry("ADM_VROOF4A", "Tiger Pattern Roof")
        AddTextEntry("ADM_VROOF5A", "Leopard Pattern Roof")
        AddTextEntry("ADM_VROOF6A", "Croc Pattern Roof")
        AddTextEntry("ADM_VROOF7A", "Flying Bravo Roof")
        AddTextEntry("ADM_VROOF8A", "Colorable Flying Bravo Roof")
        AddTextEntry("ADM_VROOF1B", "Leather Vinyl Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF2B", "Vinyl Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF3B", "Zebra Pattern Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF4B", "Tiger Pattern Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF5B", "Leopard Pattern Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF6B", "Croc Pattern Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF7B", "Flying Bravo Roof w/ Gold Trim")
        AddTextEntry("ADM_VROOF8B", "Colorable Flying Bravo Roof w/ Gold Trim")

        AddTextEntry("ADM_BOOTTRIM1A", "Painted Trim")
        AddTextEntry("ADM_BOOTTRIM1B", "Gold Trim")
        AddTextEntry("ADM_BOOTTRIM2A", "Half Painted Trim")
        AddTextEntry("ADM_BOOTTRIM2B", "Half Chrome Trim")
        AddTextEntry("ADM_BOOTTRIM2C", "Half Gold Trim")
        AddTextEntry("ADM_BOOTTRIM3A", "Top Chrome Trim")
        AddTextEntry("ADM_BOOTTRIM3B", "Full Chrome Trim")
        AddTextEntry("ADM_BOOTTRIM4A", "Top Gold Trim")
        AddTextEntry("ADM_BOOTTRIM4B", "Full Gold Trim")
        
        AddTextEntry("ADM_BTRIM1A", "Painted Side Trim")
        AddTextEntry("ADM_BTRIM1B", "Gold Side Trim")
        AddTextEntry("ADM_BTRIM2A", "Chrome Side Cladding")
        AddTextEntry("ADM_BTRIM2B", "Painted Side Cladding")
        AddTextEntry("ADM_BTRIM2C", "Gold Side Cladding")

        AddTextEntry("ADM_OFEND1A", "Chrome Overfenders")
        AddTextEntry("ADM_BTRIM1A", "Painted Side Trim")
        AddTextEntry("ADM_BTRIM1AO", "Painted Side Trim w/ Overfenders")
        AddTextEntry("ADM_BTRIM1B", "Gold Side Trim")
        AddTextEntry("ADM_BTRIM1BO", "Gold Side Trim w/ Overfenders")
        AddTextEntry("ADM_BTRIM2A", "Chrome Side Cladding")
        AddTextEntry("ADM_BTRIM2AO", "Chrome Side Cladding w/ Overfenders")
        AddTextEntry("ADM_BTRIM2B", "Painted Side Cladding")
        AddTextEntry("ADM_BTRIM2BO", "Painted Side Cladding w/ Overfenders")
        AddTextEntry("ADM_BTRIM2C", "Gold Side Cladding")
        AddTextEntry("ADM_BTRIM2CO", "Gold Side Cladding w/ Overfenders")

        AddTextEntry("ADM_BSTRIM1A", "Chrome Side Panel Trim")
        AddTextEntry("ADM_BSTRIM1B", "Gold Side Panel Trim")
end)
