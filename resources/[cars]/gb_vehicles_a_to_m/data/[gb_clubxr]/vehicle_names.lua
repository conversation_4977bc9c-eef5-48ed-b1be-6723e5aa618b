Citizen.CreateThread(function()
AddTextEntry("GBCLUBXR", "Club XR")
AddTextEntry("CLUBXR_BUMF1", "Sport Front Bumper")
AddTextEntry("CLUBXR_BUMR1", "Mid Level Rear Bumper")
AddTextEntry("CLUBXR_BUMR2", "Sport Rear Bumper")
AddTextEntry("CLUBXR_GRILL1", "Dual Slat Grill")
AddTextEntry("CLUBXR_GRILL2", "Open Grill")
AddTextEntry("CLUBXR_SPLT1", "Custom Splitter")
AddTextEntry("CLUBXR_SPLT2", "Track Splitter")
AddTextEntry("CLUBXR_SPLT3", "Accent Splitter")
AddTextEntry("CLUBXR_SPLT3A", "Carbon Accent Splitter")
AddTextEntry("CLUBXR_SPLT3B", "Painted Accent Splitter")
AddTextEntry("CLUBXR_SPLT4", "Super Splitter")
AddTextEntry("CLUBXR_SPLT4A", "Carbon Super Splitter")
AddTextEntry("CLUBXR_SPLT4B", "Painted Super Splitter")
AddTextEntry("CLUBXR_SPLT5", "Extreme Splitter")
AddTextEntry("CLUBXR_SPLT5A", "Carbon Extreme Splitter")
AddTextEntry("CLUBXR_SPLT5B", "Painted Extreme Splitter")
AddTextEntry("CLUBXR_DIFF1", "Custom Diffuser")
AddTextEntry("CLUBXR_DIFF2", "Track Diffuser")
AddTextEntry("CLUBXR_SKIRT1", "Custom Skirt")
AddTextEntry("CLUBXR_SKIRT1A", "Carbon Custom Skirt")
AddTextEntry("CLUBXR_SKIRT2", "Carbon Street Skirt")
AddTextEntry("CLUBXR_SKIRT3", "Race Skirt")
AddTextEntry("CLUBXR_SKIRT3A", "Carbon Race Skirt")
AddTextEntry("CLUBXR_SKIRT4", "Track Skirt")
AddTextEntry("CLUBXR_SKIRT4A", "Carbon Track Skirt")
AddTextEntry("CLUBXR_SKIRT5", "Super Skirt")
AddTextEntry("CLUBXR_SKIRT5A", "Carbon Super Skirt")
AddTextEntry("CLUBXR_CANARD1", "Front Canards")
AddTextEntry("CLUBXR_CANARD2", "Extreme Front Canards")
AddTextEntry("CLUBXR_RSCOOP1", "Track Roof Scoop")
AddTextEntry("CLUBXR_RSCOOP2", "Custom Roof Scoop")
AddTextEntry("CLUBXR_RSCOOP3", "Street Roof Scoop")
AddTextEntry("CLUBXR_RSCOOP4", "Race Roof Scoop")
AddTextEntry("CLUBXR_RSCOOP5", "Track Roof Scoop")
AddTextEntry("CLUBXR_RSCOOP6", "Competition Roof Scoop")
AddTextEntry("CLUBXR_AERIAL1", "Factory Shark Fin")
AddTextEntry("CLUBXR_AERIAL2", "Carbon Shark Fin")
AddTextEntry("CLUBXR_AERIAL3", "Snub Shark Fin")
AddTextEntry("CLUBXR_AERIAL4", "Race Aerials")
AddTextEntry("CLUBXR_AERIAL5", "Track Aerials")
AddTextEntry("CLUBXR_WING1", "Street Spoiler")
AddTextEntry("CLUBXR_WING1A", "Carbon Street Spoiler")
AddTextEntry("CLUBXR_WING2", "Track Wing")
AddTextEntry("CLUBXR_WING3", "Track Wing MK2")
AddTextEntry("CLUBXR_WING4", "Circuit Wing")
AddTextEntry("CLUBXR_WING5", "Sprint Wing")
AddTextEntry("CLUBXR_WING6", "Time Attack Wing")
AddTextEntry("TOP_BUMFXR", "Front Bumper")

AddTextEntry("GBCLUBXR_LIV1", "RBS Performance")
AddTextEntry("GBCLUBXR_LIV2", "Sporting Stripes (Black)")
AddTextEntry("GBCLUBXR_LIV3", "Sporting Stripes (Blue)")
AddTextEntry("GBCLUBXR_LIV4", "Sporting Stripes (Lime)")
AddTextEntry("GBCLUBXR_LIV5", "Sporting Stripes (Orange)")
AddTextEntry("GBCLUBXR_LIV6", "Sporting Stripes (Pink)")
AddTextEntry("GBCLUBXR_LIV7", "Sporting Stripes (Red)")
AddTextEntry("GBCLUBXR_LIV8", "Sporting Stripes (White)")
AddTextEntry("GBCLUBXR_LIV9", "XR Decals (Black)")
AddTextEntry("GBCLUBXR_LIV10", "XR Decals (Gray)")
AddTextEntry("GBCLUBXR_LIV11", "XR Decals (White)")
end)