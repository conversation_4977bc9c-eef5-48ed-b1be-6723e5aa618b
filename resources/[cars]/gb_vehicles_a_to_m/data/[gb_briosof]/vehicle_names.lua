Citizen.CreateThread(function()
AddTextEntry("BRIOSO<PERSON>", "Brioso Fulmine")
AddTextEntry("BRIOSOF_BUMF0A", "Painted Stock Front Bumper")
AddTextEntry("BRIOSOF_BUMF1", "Vented Front Bumper")
AddTextEntry("BRIOSOF_BUMF1A", "Pained Vented Front Bumper")
AddTextEntry("BRIOSOF_BUMF2", "R/E Front Bumper")
AddTextEntry("BRIOSOF_BUMF2A", "Vented R/E Front Bumper")
AddTextEntry("BRIOSOF_BUMR0A", "Painted Stock Rear Bumper")
AddTextEntry("BRIOSOF_BUMR1", "R/E Rear Bumper")
AddTextEntry("BRIOSOF_SPLT1", "Custom Splitter")
AddTextEntry("BRIOSOF_SPLT2", "Track Splitter")
AddTextEntry("BRIOSOF_SPLT3", "Accent Splitter")
AddTextEntry("BRIOSOF_SPLT3A", "Carbon Accent Splitter")
AddTextEntry("BRIOSOF_SPLT3B", "Painted Accent Splitter")
AddTextEntry("BRIOSOF_SPLT4", "Super Splitter")
AddTextEntry("BRIOSOF_SPLT4A", "Carbon Super Splitter")
AddTextEntry("BRIOSOF_SPLT4B", "Painted Super Splitter")
AddTextEntry("BRIOSOF_SPLT5", "Extreme Splitter")
AddTextEntry("BRIOSOF_SPLT5A", "Carbon Extreme Splitter")
AddTextEntry("BRIOSOF_SPLT5B", "Painted Extreme Splitter")
AddTextEntry("BRIOSOF_DIFF1", "Custom Diffuser")
AddTextEntry("BRIOSOF_DIFF2", "Track Diffuser")
AddTextEntry("BRIOSOF_TRUNK1", "Painted Rear Badge")
AddTextEntry("BRIOSOF_TRUNK2", "Rear Debadge")
AddTextEntry("BRIOSOF_TRUNK3", "Vented Rear Trunk")
AddTextEntry("BRIOSOF_SKIRT0", "R/E Skirt")
AddTextEntry("BRIOSOF_SKIRT1", "Custom Skirt")
AddTextEntry("BRIOSOF_SKIRT1A", "Carbon Custom Skirt")
AddTextEntry("BRIOSOF_SKIRT2", "Carbon Street Skirt")
AddTextEntry("BRIOSOF_SKIRT3", "Race Skirt")
AddTextEntry("BRIOSOF_SKIRT3A", "Carbon Race Skirt")
AddTextEntry("BRIOSOF_SKIRT4", "Track Skirt")
AddTextEntry("BRIOSOF_SKIRT4A", "Carbon Track Skirt")
AddTextEntry("BRIOSOF_SKIRT5", "Super Skirt")
AddTextEntry("BRIOSOF_SKIRT5A", "Carbon Super Skirt")
AddTextEntry("BRIOSOF_HOOD1", "Center Bulge Hood")
AddTextEntry("BRIOSOF_HOOD2", "Time Attack Hood")
AddTextEntry("BRIOSOF_RSCOOP1", "Track Roof Scoop")
AddTextEntry("BRIOSOF_RSCOOP2", "Custom Roof Scoop")
AddTextEntry("BRIOSOF_RSCOOP3", "Street Roof Scoop")
AddTextEntry("BRIOSOF_RSCOOP4", "Race Roof Scoop")
AddTextEntry("BRIOSOF_RSCOOP5", "Track Roof Scoop")
AddTextEntry("BRIOSOF_RSCOOP6", "Competition Roof Scoop")
AddTextEntry("BRIOSOF_AERIAL1", "Factory Shark Fin")
AddTextEntry("BRIOSOF_AERIAL2", "Carbon Shark Fin")
AddTextEntry("BRIOSOF_AERIAL3", "Snub Shark Fin")
AddTextEntry("BRIOSOF_AERIAL4", "Race Aerials")
AddTextEntry("BRIOSOF_AERIAL5", "Track Aerials")
AddTextEntry("BRIOSOF_WING1", "Street Spoiler")
AddTextEntry("BRIOSOF_WING2", "Track Wing")
AddTextEntry("BRIOSOF_WING3", "Track Wing MK2")
AddTextEntry("BRIOSOF_WING4", "Circuit Wing")
AddTextEntry("BRIOSOF_WING5", "Sprint Wing")
AddTextEntry("BRIOSOF_WING6", "Time Attack Wing")
AddTextEntry("BRIOSOF_TRIM", "Painted Trim")

AddTextEntry("BRIOSOF_LIV1", "Icona Bianca")
AddTextEntry("BRIOSOF_LIV2", "Icona Nera")
AddTextEntry("BRIOSOF_LIV3", "Icona Rossa")
AddTextEntry("BRIOSOF_LIV4", "Icona Argento")
AddTextEntry("BRIOSOF_LIV5", "Black Stripe")
AddTextEntry("BRIOSOF_LIV6", "White Stripe")
AddTextEntry("BRIOSOF_LIV7", "Red Stripe")
AddTextEntry("BRIOSOF_LIV8", "Yellow Stripe")
AddTextEntry("BRIOSOF_LIV9", "Blue Stripe")
AddTextEntry("BRIOSOF_LIV10", "Pink Stripe")
AddTextEntry("BRIOSOF_LIV11", "L'Originale White")
AddTextEntry("BRIOSOF_LIV12", "L'Originale Black")
AddTextEntry("BRIOSOF_LIV13", "Dama Black")
AddTextEntry("BRIOSOF_LIV14", "Dama White")
AddTextEntry("BRIOSOF_LIV15", "Dama Red")
AddTextEntry("BRIOSOF_LIV16", "Half Green")
AddTextEntry("BRIOSOF_LIV17", "Half Red")
AddTextEntry("BRIOSOF_LIV18", "Tricolor")
AddTextEntry("BRIOSOF_LIV19", "70° Anniversario")
AddTextEntry("BRIOSOF_LIV20", "75° Anniversario")
AddTextEntry("BRIOSOF_LIV21", "The Nemo Special")
AddTextEntry("BRIOSOF_LIV22", "Pizza This")
AddTextEntry("BRIOSOF_LIV23", "Burgershot")
AddTextEntry("BRIOSOF_LIV24", "CatCafe")
end)