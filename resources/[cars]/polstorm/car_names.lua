Citizen.CreateThread(function()
	AddTextEntryByHash(`P<PERSON><PERSON>OR<PERSON>`, "Sandstorm Police")
	AddTextEntryByHash(`POLSTORMXL`, "Sandstorm XL Police")

	AddTextEntryByHash(`POLICE_ANTENNA`, "Antenna")
	AddTextEntryByHash(`POLICE_DIVIDER`, "Divider")
	AddTextEntryByHash(`CALLSIGN_L`, "Left CallSign")
	AddTextEntryByHash(`CALLSIGN_C`, "Center CallSign")
	AddTextEntryByHash(`CALLSIGN_R`, "Right CallSign")
	AddTextEntryByHash(`CALLSIGN_0`, "0")
	AddTextEntryByHash(`CALLSIGN_1`, "1")
	AddTextEntryByHash(`CALLSIGN_2`, "2")
	AddTextEntryByHash(`CALLSIGN_3`, "3")
	AddTextEntryByHash(`CALLSIGN_4`, "4")
	AddTextEntryByHash(`CALLSIGN_5`, "5")
	AddTextEntryByHash(`CALLSIGN_6`, "6")
	AddTextEntryByHash(`CALLSIGN_7`, "7")
	AddTextEntryByHash(`CALLSIGN_8`, "8")
	AddTextEntryByHash(`CALLSIGN_9`, "9")
	AddTextEntryByHash(`POLICE_LIV`, "Police Livery")

end)