<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVarGlobal>
  <Kits>
    <Item>
      <kitName>8005_polstorm_modkit</kitName>
      <id value="8005" />
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods>
        <Item>
          <modelName>polstorm_liv1</modelName>
          <modShopLabel>LSPD_LIV</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_liv2</modelName>
          <modShopLabel>LIV_BCSO</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_liv3</modelName>
          <modShopLabel>LIV_BCSO</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_liv4</modelName>
          <modShopLabel>LIV_SASP</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_FLAPS -->
        <Item>
          <modelName>polstorm_flap1</modelName>
          <modShopLabel>polstorm_flap1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_flap1</modelName>
          <modShopLabel>polstorm_flap2</modShopLabel>
          <linkedModels>
            <Item>polstorm_flapflags1</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_TRIM -->
        <Item>
          <modelName>polstorm_trimdsidef1</modelName>
          <modShopLabel>polstorm_trimdsidef1</modShopLabel>
          <linkedModels>
            <Item>polstorm_trimdsider1</Item>
            <Item>polstorm_trimpsider1</Item>
            <Item>polstorm_trimpsidef1</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_r</Item>
            <Item>misc_k</Item>
            <Item>misc_m</Item>
            <Item>misc_n</Item>
          </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimdsidef2</modelName>
          <modShopLabel>polstorm_trimdsidef2</modShopLabel>
          <linkedModels>
            <Item>polstorm_trimdsider2</Item>
            <Item>polstorm_trimpsider2</Item>
            <Item>polstorm_trimpsidef2</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_r</Item>
            <Item>misc_k</Item>
            <Item>misc_m</Item>
            <Item>misc_n</Item>
          </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_MIRROR -->
        <Item>
          <modelName>polstorm_mirror1l</modelName>
          <modShopLabel>polstorm_mirror1l</modShopLabel>
          <linkedModels>
            <Item>polstorm_mirror1r</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_j</Item>
            <Item>misc_i</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_mirror2l</modelName>
          <modShopLabel>polstorm_mirror2l</modShopLabel>
          <linkedModels>
            <Item>polstorm_mirror2r</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_j</Item>
            <Item>misc_i</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_FBUMPER -->
        <Item>
          <modelName>polstorm_fbumperlip</modelName>
          <modShopLabel>polstorm_fbumperlip1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper1</modelName>
          <modShopLabel>polstorm_fbumper1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper1</modelName>
          <modShopLabel>polstorm_fbumper1lip</modShopLabel>
          <linkedModels>
            <Item>polstorm_fbumperlip</Item>
          </linkedModels>
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper2</modelName>
          <modShopLabel>polstorm_fbumper2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper2</modelName>
          <modShopLabel>polstorm_fbumper2lip</modShopLabel>
          <linkedModels>
            <Item>polstorm_fbumperlip</Item>
          </linkedModels>
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper3</modelName>
          <modShopLabel>polstorm_fbumper3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper4</modelName>
          <modShopLabel>polstorm_fbumper4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper4a</modelName>
          <modShopLabel>polstorm_fbumper4a</modShopLabel>
          <linkedModels>
            <Item>polstorm_fbumper4</Item>
          </linkedModels>
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>mod_col_3</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_RBUMPER -->
        <Item>
          <modelName>polstorm_rbumper1</modelName>
          <modShopLabel>polstorm_rbumper1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_rbumper2</modelName>
          <modShopLabel>polstorm_rbumper2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_BED -->
        <Item>
          <modelName>polstorm_winga</modelName>
          <modShopLabel>polstorm_winga</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassi</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_wingc</modelName>
          <modShopLabel>polstorm_wingc</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>boot</item>
          </turnOffBones>
          <type>VMT_CHASSIS3</type>
          <bone>chassi</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_wingd</modelName>
          <modShopLabel>polstorm_wingd</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>cargodoor</bone>
          <collisionBone>mod_col_5</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_ GRILLE -->
        <Item>
          <modelName>polstorm_grille1</modelName>
          <modShopLabel>polstorm_grille1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>misc_b</item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_grille2</modelName>
          <modShopLabel>polstorm_grille2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>misc_b</item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_grille3</modelName>
          <modShopLabel>polstorm_grille3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>misc_b</item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- CALL SIGNS -->
        <Item>
          <modelName>polstorm_sign_a0</modelName>
          <modShopLabel>CALLSIGN_0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a1</modelName>
          <modShopLabel>CALLSIGN_1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a2</modelName>
          <modShopLabel>CALLSIGN_2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a3</modelName>
          <modShopLabel>CALLSIGN_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a4</modelName>
          <modShopLabel>CALLSIGN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a5</modelName>
          <modShopLabel>CALLSIGN_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a6</modelName>
          <modShopLabel>CALLSIGN_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a7</modelName>
          <modShopLabel>CALLSIGN_7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a8</modelName>
          <modShopLabel>CALLSIGN_8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a9</modelName>
          <modShopLabel>CALLSIGN_9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b0</modelName>
          <modShopLabel>CALLSIGN_0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b1</modelName>
          <modShopLabel>CALLSIGN_1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b2</modelName>
          <modShopLabel>CALLSIGN_2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b3</modelName>
          <modShopLabel>CALLSIGN_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b4</modelName>
          <modShopLabel>CALLSIGN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b5</modelName>
          <modShopLabel>CALLSIGN_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b6</modelName>
          <modShopLabel>CALLSIGN_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b7</modelName>
          <modShopLabel>CALLSIGN_7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b8</modelName>
          <modShopLabel>CALLSIGN_8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b9</modelName>
          <modShopLabel>CALLSIGN_9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c0</modelName>
          <modShopLabel>CALLSIGN_0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c1</modelName>
          <modShopLabel>CALLSIGN_1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c2</modelName>
          <modShopLabel>CALLSIGN_2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c3</modelName>
          <modShopLabel>CALLSIGN_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c4</modelName>
          <modShopLabel>CALLSIGN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c5</modelName>
          <modShopLabel>CALLSIGN_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c6</modelName>
          <modShopLabel>CALLSIGN_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c7</modelName>
          <modShopLabel>CALLSIGN_7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c8</modelName>
          <modShopLabel>CALLSIGN_8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c9</modelName>
          <modShopLabel>CALLSIGN_9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      </visibleMods>
      <linkMods>
        <Item>
          <modelName>polstorm_mirror1r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_mirror2r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_flapflags1</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_wingc</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimdsider1</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsider1</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsidef1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimdsider2</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsider2</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsidef2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumperlip</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper4</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="true" />
        </Item>
      </linkMods>
      <statMods>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="200" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="5" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="15" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_01</identifier>
          <modifier value="3851180092" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_01_Preview</identifier>
          <modifier value="246182814" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_02</identifier>
          <modifier value="3412861948" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_02_Preview</identifier>
          <modifier value="1804608241" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_03</identifier>
          <modifier value="3374260066" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_03_Preview</identifier>
          <modifier value="2798044638" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
      <slotNames>
        <Item>
          <slot>VMT_CHASSIS</slot>
          <name>TOP_CAGE</name>
        </Item>
        <Item>
          <slot>VMT_SKIRT</slot>
          <name>TOP_MUD</name>
        </Item>
        <Item>
          <slot>VMT_WING_L</slot>
          <name>CALLSIGN_L</name>
        </Item>
        <Item>
          <slot>VMT_CHASSIS4</slot>
          <name>CALLSIGN_C</name>
        </Item>
        <Item>
          <slot>VMT_ROOF</slot>
          <name>CALLSIGN_R</name>
        </Item>
      </slotNames>
      <liveryNames />
    </Item>
    <Item>
      <kitName>8006_polstormxl_modkit</kitName>
      <id value="8006" />
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods>
        <!-- SANDSTORM_LIVERY -->
        <Item>
          <modelName>polstorm_liv1</modelName>
          <modShopLabel>LSPD_LIV</modShopLabel>
          <linkedModels />
          <turnOffBones>
          </turnOffBones>
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_FLAPS -->
        <Item>
          <modelName>polstormxl_flap1</modelName>
          <modShopLabel>polstormxl_flap1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstormxl_flap1</modelName>
          <modShopLabel>polstormxl_flap2</modShopLabel>
          <linkedModels>
            <Item>polstormxl_flapflags1</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_TRIM -->
        <Item>
          <modelName>polstorm_trimdsidef1</modelName>
          <modShopLabel>polstorm_trimdsidef1</modShopLabel>
          <linkedModels>
            <Item>polstorm_trimdsider1</Item>
            <Item>polstorm_trimpsider1</Item>
            <Item>polstorm_trimpsidef1</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_r</Item>
            <Item>misc_k</Item>
            <Item>misc_m</Item>
            <Item>misc_n</Item>
          </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimdsidef2</modelName>
          <modShopLabel>polstorm_trimdsidef2</modShopLabel>
          <linkedModels>
            <Item>polstorm_trimdsider2</Item>
            <Item>polstorm_trimpsider2</Item>
            <Item>polstorm_trimpsidef2</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_r</Item>
            <Item>misc_k</Item>
            <Item>misc_m</Item>
            <Item>misc_n</Item>
          </turnOffBones>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_MIRROR -->
        <Item>
          <modelName>polstorm_mirror1l</modelName>
          <modShopLabel>polstorm_mirror1l</modShopLabel>
          <linkedModels>
            <Item>polstorm_mirror1r</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_j</Item>
            <Item>misc_i</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_mirror2l</modelName>
          <modShopLabel>polstorm_mirror2l</modShopLabel>
          <linkedModels>
            <Item>polstorm_mirror2r</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_j</Item>
            <Item>misc_i</Item>
          </turnOffBones>
          <type>VMT_DOOR_R</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassi</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_FBUMPER -->
        <Item>
          <modelName>polstorm_fbumperlip</modelName>
          <modShopLabel>polstorm_fbumperlip1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper1</modelName>
          <modShopLabel>polstorm_fbumper1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper1</modelName>
          <modShopLabel>polstorm_fbumper1lip</modShopLabel>
          <linkedModels>
            <Item>polstorm_fbumperlip</Item>
          </linkedModels>
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper2</modelName>
          <modShopLabel>polstorm_fbumper2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper2</modelName>
          <modShopLabel>polstorm_fbumper2lip</modShopLabel>
          <linkedModels>
            <Item>polstorm_fbumperlip</Item>
          </linkedModels>
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper3</modelName>
          <modShopLabel>polstorm_fbumper3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper4</modelName>
          <modShopLabel>polstorm_fbumper4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper4a</modelName>
          <modShopLabel>polstorm_fbumper4a</modShopLabel>
          <linkedModels>
            <Item>polstorm_fbumper4</Item>
          </linkedModels>
          <turnOffBones>
            <Item>bumper_f</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>mod_col_3</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_RBUMPER -->
        <Item>
          <modelName>polstorm_rbumper1</modelName>
          <modShopLabel>polstorm_rbumper1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_rbumper2</modelName>
          <modShopLabel>polstorm_rbumper2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_BED -->
        <Item>
          <modelName>polstorm_winga</modelName>
          <modShopLabel>polstorm_winga</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>chassi</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_wingc</modelName>
          <modShopLabel>polstorm_wingc</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>boot</item>
          </turnOffBones>
          <type>VMT_CHASSIS3</type>
          <bone>chassi</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- SANDSTORM_ GRILLE -->
        <Item>
          <modelName>polstorm_grille1</modelName>
          <modShopLabel>polstorm_grille1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>misc_b</item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_grille2</modelName>
          <modShopLabel>polstorm_grille2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>misc_b</item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_grille3</modelName>
          <modShopLabel>polstorm_grille3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <item>misc_b</item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="true" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- CALL SIGNS -->
        <Item>
          <modelName>polstorm_sign_a0</modelName>
          <modShopLabel>CALLSIGN_0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a1</modelName>
          <modShopLabel>CALLSIGN_1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a2</modelName>
          <modShopLabel>CALLSIGN_2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a3</modelName>
          <modShopLabel>CALLSIGN_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a4</modelName>
          <modShopLabel>CALLSIGN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a5</modelName>
          <modShopLabel>CALLSIGN_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a6</modelName>
          <modShopLabel>CALLSIGN_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a7</modelName>
          <modShopLabel>CALLSIGN_7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a8</modelName>
          <modShopLabel>CALLSIGN_8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_a9</modelName>
          <modShopLabel>CALLSIGN_9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b0</modelName>
          <modShopLabel>CALLSIGN_0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b1</modelName>
          <modShopLabel>CALLSIGN_1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b2</modelName>
          <modShopLabel>CALLSIGN_2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b3</modelName>
          <modShopLabel>CALLSIGN_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b4</modelName>
          <modShopLabel>CALLSIGN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b5</modelName>
          <modShopLabel>CALLSIGN_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b6</modelName>
          <modShopLabel>CALLSIGN_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b7</modelName>
          <modShopLabel>CALLSIGN_7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b8</modelName>
          <modShopLabel>CALLSIGN_8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_b9</modelName>
          <modShopLabel>CALLSIGN_9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c0</modelName>
          <modShopLabel>CALLSIGN_0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c1</modelName>
          <modShopLabel>CALLSIGN_1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c2</modelName>
          <modShopLabel>CALLSIGN_2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c3</modelName>
          <modShopLabel>CALLSIGN_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c4</modelName>
          <modShopLabel>CALLSIGN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c5</modelName>
          <modShopLabel>CALLSIGN_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c6</modelName>
          <modShopLabel>CALLSIGN_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c7</modelName>
          <modShopLabel>CALLSIGN_7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c8</modelName>
          <modShopLabel>CALLSIGN_8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>polstorm_sign_c9</modelName>
          <modShopLabel>CALLSIGN_9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_WING_L</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      </visibleMods>
      <linkMods>
        <Item>
          <modelName>polstorm_mirror1r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_mirror2r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstormxl_flapflags1</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_wingc</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimdsider1</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsider1</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsidef1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimdsider2</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsider2</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_trimpsidef2</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumperlip</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="true" />
        </Item>
        <Item>
          <modelName>polstorm_fbumper4</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="true" />
        </Item>
      </linkMods>
      <statMods>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="200" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="5" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="15" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_01</identifier>
          <modifier value="3851180092" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_01_Preview</identifier>
          <modifier value="246182814" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_02</identifier>
          <modifier value="3412861948" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_02_Preview</identifier>
          <modifier value="1804608241" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_03</identifier>
          <modifier value="3374260066" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_03_Preview</identifier>
          <modifier value="2798044638" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
      <slotNames>
        <Item>
          <slot>VMT_CHASSIS</slot>
          <name>TOP_CAGE</name>
        </Item>
        <Item>
          <slot>VMT_SKIRT</slot>
          <name>TOP_MUD</name>
        </Item>
        <Item>
          <slot>VMT_WING_L</slot>
          <name>CALLSIGN_L</name>
        </Item>
        <Item>
          <slot>VMT_CHASSIS4</slot>
          <name>CALLSIGN_C</name>
        </Item>
        <Item>
          <slot>VMT_ROOF</slot>
          <name>CALLSIGN_R</name>
        </Item>
      </slotNames>
      <liveryNames />
    </Item>
  </Kits>
  <Sirens>
    <Item>
      <id value="240" />
      <name>PoliceLights1</name>
      <timeMultiplier value="1" />
      <lightFalloffMax value="80" />
      <lightFalloffExponent value="55" />
      <lightInnerConeAngle value="2" />
      <lightOuterConeAngle value="70" />
      <lightOffset value="0" />
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="250" />
      <leftHeadLight>
        <sequencer value="0" />
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0" />
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="1431655765" />
      </leftTailLight>
      <rightTailLight>
        <sequencer value="2863311530" />
      </rightTailLight>
      <leftHeadLightMultiples value="1" />
      <rightHeadLightMultiples value="1" />
      <leftTailLightMultiples value="1" />
      <rightTailLightMultiples value="1" />
      <useRealLights value="true" />
      <sirens>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="1.57079637" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1091915781" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="580210965" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="341132369" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="137708865" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="137011874" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="340435498" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="579512458" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="-1.57079637" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1091217410" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1431655765" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="715827882" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1431655762" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="716526933" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="715827885" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1430956714" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1431655765" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="715827882" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1431655762" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="716526933" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="715827885" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <rotation>
            <delta value="0.0" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="0" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="false" />
          </rotation>
          <flashiness>
            <delta value="3.14159274" />
            <start value="0.0" />
            <speed value="0.0" />
            <sequencer value="1430956714" />
            <multiples value="1" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="0.00000001"/>
            <size value="0.00000000"/>
            <pull value="0.15000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF" />
          <intensity value="1.0" />
          <lightGroup value="0" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
      </sirens>
    </Item>
  </Sirens>
</CVehicleModelInfoVarGlobal>