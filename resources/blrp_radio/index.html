<html>
<head>
    <script src="nui://game/ui/jquery.js" type="text/javascript"></script>

    <!--        <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>-->

    <link rel="stylesheet" href="css/style.css">
    <title>BLRP Radio</title>
</head>
<body>
<div id="radio">
    <img class="background" style="display: none" src="img/radio-off.png" id="bg-off"/>
    <img class="background" id="bg-on" src="img/radio-on.png"/>

    <img class="background" style="display: none" id="button-overlay-left" src="img/button-overlay-left.png"/>
    <img class="background" style="display: none" id="button-overlay-middle" src="img/button-overlay-middle.png"/>
    <img class="background" style="display: none" id="button-overlay-right" src="img/button-overlay-right.png"/>
    <img class="background" style="display: none" id="button-overlay-power-off" src="img/button-overlay-power-off.png"/>
    <img class="background" style="display: none" id="button-overlay-power-on" src="img/button-overlay-power-on.png"/>

    <button class="button-control" id="button-power"></button>
    <button class="button-control button-bottom" id="button-left"></button>
    <button class="button-control button-bottom" id="button-middle"></button>
    <button class="button-control button-bottom" id="button-right"></button>

    <div id="screen">
        <img src="img/battery.png" id="status-battery"/>
        <img src="img/gps.png" id="status-gps"/>
        <img src="img/rssi.png" id="status-rssi"/>

        <div id="main-screen">
            <input type="number" id="talkgroup" min="1" max="15000" placeholder="100-500">
            <button class="spinner-control" id="spinner-up" type="button">&#9650;</button>
            <button class="spinner-control" id="spinner-down" type="button">&#9660;</button>
            <span id="alias"></span>
            <span style="display: none" id="text-off">RADIO OFF</span>
        </div>

        <!-- Channel list overlay -->
        <div id="channel-list-overlay" style="display: none;">
            <div class="channel-list-container">
                <h3>Channel List</h3>
                <div id="active-channels"></div>
                <div class="channel-controls">
                    <button id="add-channel-btn" class="icon-btn">+</button>
                    <button id="available-channels-btn" class="icon-btn">📻</button>
                    <button id="close-list-btn" class="icon-btn">×</button>
                </div>
            </div>
        </div>

        <!-- Available channels overlay -->
        <div id="available-channels-overlay" style="display: none;">
            <div class="channel-list-container">
                <h3>Available Channels</h3>
                <div id="available-channels-list"></div>
                <div class="channel-controls">
                    <button id="back-to-channels-btn" class="icon-btn">←</button>
                    <button id="close-available-btn" class="icon-btn">×</button>
                </div>
            </div>
        </div>

        <!-- Channel add prompt -->
        <div class="channel-prompt" style="display: none;">
            <input type="number" id="new-channel-input" placeholder="Enter channel (1-500)" step="0.01" min="1" max="15000">
            <div>
                <button id="add-channel-confirm">Add</button>
                <button id="add-channel-cancel">Cancel</button>
            </div>
        </div>
    </div>

    <div class="control-labels" id="control-labels-primary">
        <span class="label-left">Home</span>
        <span class="label-middle">Chan</span>
        <span class="label-right">Set</span>
    </div>

    <div class="control-labels" id="control-labels-secondary" style="display: none">
        <span class="label-left">Glob</span>
        <span class="label-middle">Vol-</span>
        <span class="label-right">Vol+</span>
    </div>


</div>

<script type="text/javascript">
  let interfaceShowing = false;
  let on = true;
  let cachedChannelPowerDown = null;
  let cachedChannel = 0;
  let cachedAlias = null;
  let lockout = false;
  let quickKeysModified = false;
  // Add array to track active channels
  let activeChannels = [];
  let currentChannel = 0;

  document.onkeydown = function (evt) {
    if (!interfaceShowing) return false;

    evt = evt || window.event;

    // Add new handler for 'C' key (keyCode 67) to open channel list
    if (evt.keyCode === 67 && on) {
      toggleChannelList();
    }

    // Keep your existing key handlers
    if (evt.keyCode === 27 || evt.keyCode === 74) {
      hideInterface();
    }

    if (evt.keyCode === 13 && !quickKeysModified) {
      if ($('.channel-prompt').is(':visible')) {
        $("#add-channel-confirm").click();
      } else {
        $("#button-right").trigger('click');
      }
    }

    if (evt.keyCode === 16 && !quickKeysModified) {
      quickKeysModified = true

      $('#control-labels-primary').hide()
      $('#control-labels-secondary').show()
    }
  }

  document.onkeyup = evt => {
    if (!interfaceShowing) return false;

    evt = evt || window.event;

    if (evt.keyCode === 16 && quickKeysModified) {
      quickKeysModified = false

      $('#control-labels-secondary').hide()
      $('#control-labels-primary').show()
    }
  }

  function showInterface() {
    interfaceShowing = true;
    $('body').show();
  }

  function hideInterface() {
    if (interfaceShowing) {
      sendEvent('escape')
    }

    interfaceShowing = false;
    $('body').hide();
  }

  function sendEvent(event_name, data = {}) {
    fetch(`https://blrp_radio/${event_name}`, {
      method: 'POST',
      headers: {'Content-Type': 'application/json; charset=UTF-8',},
      body: JSON.stringify(data)
    }).then(resp => resp.json()).then(resp => {
      return resp;
    });
  }

  window.addEventListener('message', function (event) {
    if (event.data.type == "interface:show") {
      showInterface();

      // Append the hint only once
      if ($("#channel-list-hint").length === 0) {
        $("<div id='channel-list-hint'>Press C for Channel List</div>").appendTo("#screen");
      } else {
        $("#channel-list-hint").show(); // Re-show if hidden
      }

      setTimeout(function () {
        $("#channel-list-hint").fadeOut(2000);
      }, 5000);
    }

    if (event.data.type == "interface:hide") {
      if (cachedChannel != 0) {
        $("#talkgroup").val(cachedChannel);
      }

      if (cachedAlias != null) {
        $("#alias").text(cachedAlias);
      }

      cachedChannel = $("#talkgroup").val();
      cachedAlias = null;

      hideInterface();
    }

    if (event.data.type == "channel:alias") {
      $("#alias").text(event.data.alias);
      $("#talkgroup").val(event.data.talkgroup);
      cachedChannel = event.data.talkgroup;
      cachedAlias = null;

      let talkgroup = $("#talkgroup").val();

      if (talkgroup == "" || talkgroup == null) {
        return;
      }

      sendEvent('updateRadioState', {
        state: on,
        channel: talkgroup
      });
    }

    if (event.data.type == "radio:setstate") {
      on = event.data.state;

      updateState();
    }

    if (event.data.type == "radio:togglestate") {
      on = !on;

      updateState();
    }

    if (event.data.type == "channel:list") {
      updateChannelList(event.data.channels, event.data.current);
    }

    if (event.data.type == "available:channels") {
      updateAvailableChannelsList(event.data.channels);
    }
  });

  function updateState() {
    let talkgroup = $("#talkgroup").val();

    if (talkgroup == "" || talkgroup == null) {
      talkgroup = 0;
    }

    sendEvent('updateRadioState', {
      state: on,
      channel: talkgroup
    });

    if (on) {
      $("#bg-on").show();
      $("#bg-off").hide();
      $("#text-off").hide();

      if (cachedChannelPowerDown != null) {
        sendEvent('joinChannel', {
          channel: cachedChannelPowerDown
        });
      }

      cachedChannelPowerDown = null;
    } else {
      sendEvent('leaveChannel');

      if (talkgroup > 0 && talkgroup < 2000) {
        cachedChannelPowerDown = talkgroup
      }

      $("#talkgroup").val("");
      $("#alias").text("");

      $("#bg-on").hide();
      $("#bg-off").show();
      $("#text-off").show();
    }
  }

  $("#button-left, #button-middle, #button-right, #button-power").hover(function () {
    let id = $(this).attr('id').split("-")[1];

    if (id == "power") {
      if (on) {
        id = "power-on";
      } else {
        id = "power-off"
      }
    } else {
      if (!on) {
        return;
      }
    }

    $("#button-overlay-" + id).show();
  }, function () {
    let id = $(this).attr('id').split("-")[1];

    if (id == "power") {
      if (on) {
        id = "power-on";
      } else {
        id = "power-off"
      }
    }

    $("#button-overlay-" + id).hide();
  });

  $("#button-power").click(function () {
    on = !on;

    $("#button-overlay-power-on").hide();
    $("#button-overlay-power-off").hide();

    updateState();
  });

  $("#button-left").click(function () {
    if (!on || lockout) {
      return;
    }

    if (quickKeysModified) {
      sendEvent('toggleGlobals')
    } else {
      sendEvent('findHome')
    }
  });

  $("#button-middle").click(function () {
    if (!on || lockout) {
      return;
    }

    if (quickKeysModified) {
      sendEvent('volumeChange', {
        direction: 'down'
      })
    } else {
      let talkgroup = $("#talkgroup").val();

      if (talkgroup == "" || talkgroup == null) {
        talkgroup = 0;
      }

      sendEvent('findNext', {
        channel: talkgroup
      })
    }
  });

  $("#button-right").click(function () {
    if (!on || lockout) {
      return;
    }

    if (quickKeysModified) {
      sendEvent('volumeChange', {
        direction: 'up'
      })
    } else {
      let talkgroup = $("#talkgroup").val();

      if (talkgroup == "" || talkgroup == null) {
        return;
      }

      talkgroup = Number(talkgroup);

      if (talkgroup % 1 != 0) {
        talkgroup = talkgroup.toFixed(2);
      }

      $("#talkgroup").val(talkgroup);

      sendEvent('joinChannel', {
        channel: talkgroup
      });
    }
  });

  $("#spinner-up").click(function () {
    let talkgroup = $("#talkgroup").val();

    if (talkgroup == "" || talkgroup == null) {
      talkgroup = 1;
      $("#talkgroup").val(talkgroup);
      $("#talkgroup").trigger("change");
      return;
    }

    talkgroup = Number(talkgroup);

    talkgroup += 1;

    if (talkgroup <= 500) {
      $("#talkgroup").val(talkgroup);
      $("#talkgroup").trigger("change");
    }
  });

  $("#spinner-down").click(function () {
    let talkgroup = $("#talkgroup").val();

    if (talkgroup == "" || talkgroup == null) {
      talkgroup = 1;
      $("#talkgroup").val(talkgroup);
      $("#talkgroup").trigger("change");
      return;
    }

    talkgroup = Number(talkgroup);

    talkgroup -= 1;

    if (talkgroup >= 1) {
      $("#talkgroup").val(talkgroup);
      $("#talkgroup").trigger("change");
    }
  });

  $('.button-control').click(function () {
    let id = $(this).attr("id");
    let target = id.split("-")[1];

    if (target == "left" || target == "middle" || target == "right") {
      if (!on || lockout) {
        return;
      }

      lockout = true;

      $(".label-" + target).css('color', '#FBD480');

      setTimeout(function () {
        $(".label-" + target).css('color', '#FFF');
      }, 400);

      setTimeout(function () {
        lockout = false;
      }, 500)
    }
  });

  $("#talkgroup").change(function () {
    if (cachedAlias == null) {
      let alias = $("#alias").text();

      if (alias != "") {
        cachedAlias = alias;
      }
    }

    $("#alias").text("");
  });


  function toggleChannelList() {
    if ($("#channel-list-overlay").is(":visible")) {
      $("#channel-list-overlay").hide();
      $("#main-screen").show(); // Show main screen when closing channel list
    } else {
      // Hide main screen when opening channel list
      $("#main-screen").hide();
      // Request updated channel list from client
      sendEvent('getChannelList');
      $("#channel-list-overlay").show();
    }
  }

  // Close channel list when close button is clicked
  $("#close-list-btn").click(function () {
    toggleChannelList();
  });

  // Add new channel button
  $("#add-channel-btn").click(function () {
    $(".channel-prompt").show();
    $("#new-channel-input").focus();
  });

  // Available channels button
  $("#available-channels-btn").click(function () {
    showAvailableChannels();
  });

  // Back to channels button
  $("#back-to-channels-btn").click(function () {
    $("#available-channels-overlay").hide();
    $("#channel-list-overlay").show();
  });

  // Close available channels button
  $("#close-available-btn").click(function () {
    $("#available-channels-overlay").hide();
    $("#main-screen").show();
  });

  // Handle channel input confirmation
  $("#add-channel-confirm").click(function () {
    let newChannel = $("#new-channel-input").val();
    if (newChannel && !isNaN(newChannel)) {
      sendEvent('addChannel', {
        channel: parseFloat(newChannel)
      });
      // Hide prompt and clear input
      $(".channel-prompt").hide();
      $("#new-channel-input").val("");
    }
  });

  // Handle channel input cancellation
  $("#add-channel-cancel").click(function () {
    $(".channel-prompt").hide();
    $("#new-channel-input").val("");
  });

  // Update the channel list UI
  function updateChannelList(channels, current) {
    activeChannels = channels.sort((a, b) => a.id - b.id); // sort by ID (talkgroup)

    currentChannel = current;

    let channelListHTML = '';

    if (activeChannels.length === 0) {
      channelListHTML = '<div class="no-channels">No active channels</div>';
    } else {
      activeChannels.forEach(channel => {
        const isActive = channel.id === current;
        channelListHTML += `
        <div class="channel-item ${isActive ? 'active' : ''}">
            <span>${channel.id} - ${channel.alias || 'Unknown'}</span>
            <div class="channel-buttons">
                ${!isActive ? `<button class="switch-btn icon-btn" data-channel="${channel.id}">⟳</button>` : ''}
                <button class="leave-btn icon-btn" data-channel="${channel.id}">×</button>
            </div>
        </div>
      `;
      });
    }

    $("#active-channels").html(channelListHTML);

    // Add event listeners for the new buttons
    $(".switch-btn").click(function () {
      const channelId = $(this).data('channel');
      sendEvent('switchChannel', {
        channel: channelId
      });
    });

    $(".leave-btn").click(function () {
      const channelId = $(this).data('channel');
      sendEvent('leaveSpecificChannel', {
        channel: channelId
      });
    });
  }

  // Show available channels
  function showAvailableChannels() {
    $("#channel-list-overlay").hide();
    $("#available-channels-overlay").show();
    // Request available channels from server
    sendEvent('getAvailableChannels');
  }

  // Update the available channels list UI
  function updateAvailableChannelsList(channels) {
    let availableChannelsHTML = '';

    if (channels.length === 0) {
      availableChannelsHTML = '<div class="no-channels">No available channels</div>';
    } else {
      // Sort channels by talkgroup ID
      channels.sort((a, b) => a.talkgroup - b.talkgroup);

      channels.forEach(channel => {
        const isAlreadyJoined = activeChannels.some(active => active.id === channel.talkgroup);
        availableChannelsHTML += `
        <div class="channel-item ${isAlreadyJoined ? 'already-joined' : ''}">
            <span>${channel.talkgroup} - ${channel.alias || channel.name || 'Unknown'}</span>
            <div class="channel-buttons">
                ${!isAlreadyJoined ? `<button class="join-btn icon-btn" data-channel="${channel.talkgroup}">+</button>` : '<span class="joined-indicator">✓</span>'}
            </div>
        </div>
      `;
      });
    }

    $("#available-channels-list").html(availableChannelsHTML);

    // Add event listeners for join buttons
    $(".join-btn").click(function () {
      const channelId = $(this).data('channel');
      sendEvent('addChannel', {
        channel: channelId
      });

      // Update the button to show it's been joined
      $(this).closest('.channel-item').addClass('already-joined');
      $(this).parent().html('<span class="joined-indicator">✓</span>');
    });
  }

</script>
</body>
</html>
