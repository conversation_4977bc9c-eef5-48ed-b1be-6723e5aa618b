AddBoxZone('PoliceEquipmentPaleto', vector3(-449.68, 6015.48, 37.0), 1.1, 0.4, {
  name = 'PoliceEquipmentPaleto',
  heading=315,
  minZ=36.05,
  maxZ=37.9
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'BCSO Supervisor Shop',

      uid = 'leo-bcso-supervisor',

      groups = {
        'sheriff_rank5',
      }
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'Police Locker',

      chest_name = 'locker_storage_char_id_4',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request BCSO ID Card',
      card_type = 'id_bcso',

      groups = {
        'Sheriff'
      }
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Paleto',

      groups = {
        'LEO',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('PoliceClothingPaleto', vector3(-438.37, 6010.36, 37.0), 2.2, 0.95, {
  name = 'PoliceClothingPaleto',
  heading=315,
  minZ=36.05,
  maxZ=38.1
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'BCSO Uniform Wardrobe',
      cloakroom_name = 'bcso',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSPD Uniform Wardrobe',
      cloakroom_name = 'lspd',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'SAHP Uniform Wardrobe',
      cloakroom_name = 'sahp',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LEO',
      },
    },
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0
})

AddBoxZone('PoliceReportBCSOPaleto', vector3(-447.13, 6013.33, 32.29), 1.6, 0.6, {
  name = 'PoliceReportBCSOPaleto',
  heading=315,
  minZ=32.09,
  maxZ=32.84
}, {
  options = {
    {
      event_client = 'blrp_voting:client:openSheriffBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in Sheriff Election',
    },
    {
      event_server = 'core:server:prison:checkParoleTime',
      icon = 'fa-regular fa-clock-desk',
      label = 'Check Parole Time',
    },
    {
      event_server = 'core:server:takePoliceReport',
      icon = 'far fa-clipboard-list',
      label = 'Make Report',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock In - BCSO',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'SAHP',

      icon = 'fas fa-clock',
      label = 'Clock in - SAHP',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0
})

AddBoxZone('PolicePaletoComputer1', vector3(-439.99, 6011.51, 27.58), 0.6, 0.6, {
  name = 'PolicePaletoComputer1',
  heading=315,
  minZ=27.38,
  maxZ=28.03,
}, {
  options = {
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:lawyer-tickets:generate',
      icon = 'fas fa-fw fa-money-check-edit',
      label = 'Write Lawyer Payment',

      groups = {
        'LEO'
      }
    },
  },
  distance = 2.0,
})

AddBoxZone('PolicePaletoComputer2', vector3(-452.74, 5996.93, 27.58), 0.6, 0.6, {
  name = 'PolicePaletoComputer2',
  heading=315,
  minZ=27.38,
  maxZ=28.03,
}, {
  options = {
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:lawyer-tickets:generate',
      icon = 'fas fa-fw fa-money-check-edit',
      label = 'Write Lawyer Payment',

      groups = {
        'LEO'
      }
    },
  },
  distance = 2.0,
})

AddBoxZone('PolicePaletoComputer3', vector3(-447.31, 6014.12, 32.29), 0.4, 0.2, {
  name = 'PolicePaletoComputer3',
  heading=315,
  minZ=32.04,
  maxZ=32.69
}, {
  options = {
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:lawyer-tickets:generate',
      icon = 'fas fa-fw fa-money-check-edit',
      label = 'Write Lawyer Payment',

      groups = {
        'LEO'
      }
    },
  },
  distance = 2.0,
})

for k, ppcomputer_coords in ipairs({
  vector3(-437.99, 6000.99, 37.0),
  vector3(-439.46, 5999.18, 37.0),
  vector3(-440.78, 5999.4, 37.0),
  vector3(-442.45, 5996.98, 37.0),
  vector3(-445.46, 5995.38, 37.01),
  vector3(-446.74, 5994.16, 37.01),
}) do
  AddBoxZone('PolicePaletoComputersTop' .. k, ppcomputer_coords, 0.6, 0.6, {
    name = 'PolicePaletoComputersTop' .. k,
    heading=315,
    minZ=36.85,
    maxZ=37.4
  }, {
    options = {
      {
        event_server = 'core:server:police:openComputer',
        icon = 'fas fa-computer-classic',
        label = 'Police Computer',

        groups = {
          'LEO',
        }
      },
      {
        event_server = 'core:server:lawyer-tickets:generate',
        icon = 'fas fa-fw fa-money-check-edit',
        label = 'Write Lawyer Payment',

        groups = {
          'LEO'
        }
      },
    },
    distance = 2.5
  })
end

AddBoxZone('PaletoSOCanteen', vector3(-451.609, 6001.786, 36.91137), 0.8, 1.2, {
  name = 'PaletoSOCanteen',
  heading = 300.0,
  minZ = 36.9,
  maxZ = 37.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Canteen',

      uid = 'leo-canteen-sandy',

      groups = {
        'LEO',
      },
    },
  },
  distance = 2.0
})
