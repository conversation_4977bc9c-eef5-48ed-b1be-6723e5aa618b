--[[
disableContext(true)

AddBoxZoneAutoname(vector4(-73.12012, 6220.495, 31.02906, 35), 2.5, 3.5, {
  minZ = 30.0,
  maxZ = 31.2,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:takeFeathers',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Animals:stage') or 1) == 9
      end
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(3522.593, -4689.335, 114.1849, 76), 1.5, 3.5, {
  minZ = 113.0,
  maxZ = 114.2,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:takeWood',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Animals:stage') or 1) == 9
      end
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(1553.543, 4640.787, 34.56285, 76), 0.4, 0.4, {
  minZ = 34.4,
  maxZ = 34.8,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:takeRock',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Animals:stage') or 1) == 9
      end
    },
  },
  distance = 2.5
})

AddTargetModel({
  `prop_xm23_deercarcass`,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:harvestDeer',
      icon = 'fas fa-shovel',
      label = 'Harvest',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Animals:stage') or 1) == 2
      end
    },
  },
  distance = 2.5
})
]]
