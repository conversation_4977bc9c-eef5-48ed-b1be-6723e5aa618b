debugPolys(true)

AddBoxZoneAutoname(vector4(964.6583, 62.24025, 72.81828, 150.0), 1.0, 1.5, {
  minZ = 72.5,
  maxZ = 73.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = 'Diamond Casino & Resort',
      location_specific = 'Membership Desk',

      groups = {
        'Diamond Casino',
      },
    },
    {
      event_server = 'core:server:casino:generateMembership',
      icon = 'far fa-id-card',
      label = 'Create VIP Membership',
      membership_type = 'id_casino_vip',

      groups = {
        'Diamond Casino',
      }
    },
    {
      event_server = 'core:server:casino:generateMembership',
      icon = 'far fa-id-card',
      label = 'Create Premier Membership',
      membership_type = 'id_casino_premier',

      groups = {
        'Diamond Casino',
      }
    },
    {
      event_server = 'core:server:casino:generateMembership',
      icon = 'far fa-id-card',
      label = 'Create Elite Membership',
      membership_type = 'id_casino_elite',

      groups = {
        'Diamond Casino',
      }
    },
    {
      event_server = 'core:server:casino:generateMembership',
      icon = 'far fa-id-card',
      label = 'Create Chairman Membership',
      membership_type = 'id_casino_chairman',

      groups = {
        'Diamond Casino',
      }
    },
  },
  distance = 1.5
})

AddTargetModel({
  `ch_prop_ch_service_locker_01a`,
  `ch_prop_ch_service_locker_01b`,
  `ch_prop_ch_service_locker_01c`,
  `ch_prop_ch_service_locker_02a`,
  `ch_prop_ch_service_locker_02b`,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      zones = {
        'CasinoLockerArea', -- In blrp_zones
      },

      groups = {
        'Diamond Casino',
      }
    },
  },
  distance = 3.0
})

--Keypad outside VIP Area
AddBoxZoneAutoname(vector4(1010.461, 78.36761, 72.0, 12.0), 0.3, 0.3, {
  minZ = 71.9,
  maxZ = 72.1,
}, {
  options = {
    {
      event_server = 'core:server:casino:swipeVIPKeycard',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Swipe keycard',
      keypad_coords = 'outer'
    },
  },

  distance = 2.5,
})

--Keypad inside VIP Area
AddBoxZoneAutoname(vector4(1007.828, 78.47469, 72.0, 192.0), 0.3, 0.3, {
  minZ = 71.9,
  maxZ = 72.1,
}, {
  options = {
    {
      event_server = 'core:server:casino:swipeVIPKeycard',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Swipe keycard',
      keypad_coords = 'inner'
    },
  },

  distance = 2.5,
})
