debugPolys(true)

AddBoxZoneAutoname(vector4(997.0933, 69.04684, 76.05361, 149.0), 1.25, 3.2, {
  minZ = 76.0,
  maxZ = 76.6,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = 'Diamond Casino & Resort',
      location_specific = 'Management Office',

      groups = {
        'Diamond Casino',
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Diamond Casino',
      component_id = 'management',
    },
  },
  distance = 4.0
})

AddBoxZoneAutoname(vector4(1003.265, 71.16087, 76.25369, 238.9), 0.8, 2.8, {
  minZ = 75.3,
  maxZ = 76.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Diamond Casino',
      component_id = 'storage',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-id-card',
      label = 'Card Supply',

      uid = 'casino-cardtable',

      business_name = 'Diamond Casino',
      component_id = 'extra_d',
    },
  },
  distance = 2.5
})
