disableContext(true)

-------------------------
-------- MANAGER --------
-------------------------

AddBoxZ<PERSON>('BeechwoodManagement1', vector3(1165.6, -404.2829, 71.78638), 0.5, 0.7, {
  name = 'BeechwoodManagement1',
  heading = 165.0,
  minZ = 71.78,
  maxZ = 72.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Beechwood Disciples',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('BeechwoodManagement2', vector3(1152.153, -406.893, 67.40532), 0.5, 0.7, {
  name = 'BeechwoodManagement2',
  heading = 255.0,
  minZ = 67.4,
  maxZ = 68.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Beechwood Disciples',
      component_id = 'management',
    },
  },
  distance = 2.5
})

-------------------------
-------- STORAGE --------
-------------------------

AddBoxZone('BeechwoodStorage1', vector3(1150.452, -419.9115, 66.58874), 1.0, 1.0, {
  name = 'BeechwoodStorage1',
  heading = 165.0,
  minZ = 66.5,
  maxZ = 68.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Beechwood Disciples',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        named = 'business102storage1',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BeechwoodStorage2', vector3(1149.38, -419.6241, 66.58874), 1.0, 1.0, {
  name = 'BeechwoodStorage2',
  heading = 165.0,
  minZ = 66.5,
  maxZ = 68.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Beechwood Disciples',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        uid = '2',
      },
    },
  },
  distance = 2.5
})

-------------------------
--------- SAFES ---------
-------------------------


AddBoxZone('BeechwoodSafe2', vector3(1166.50, -402.5458, 71.83212), 0.3, 0.8, {
  name = 'BeechwoodSafe2',
  heading = 345.0,
  minZ = 71.0,
  maxZ = 72.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Beechwood Disciples',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 100.0,
        uid = 'Safe',
      },
    },

  },
  distance = 2.5
})

-------------------------
-------- FRIDGES --------
-------------------------

AddBoxZone('BeechwoodFridge', vector3(1156.987, -404.7683, 67.53913), 0.8, 0.8, {
  name = 'BeechwoodFridge',
  heading = 255.0,
  minZ = 67.5,
  maxZ = 68.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Beechwood Disciples',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

-------------------------
-------- CLOSETS --------
-------------------------

AddBoxZone('BeechwoodCloset2', vector3(1158.82, -418.2651, 66.08831), 1.0, 1.6, {
  name = 'BeechwoodCloset2',
  heading = 345.0,
  minZ = 66.10,
  maxZ = 68.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Beechwood Disciples',
      }
    },
  },
  distance = 2.5
})

-------------------------
-------- CRAFTER --------
-------------------------

AddBoxZoneAutoname(vector4(1155.888, -413.4904, 63.96633, 255), 1.2, 2.1, {
  minZ = 64.7,
  maxZ = 65.7,
}, {
  options = {
		{
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Machete',

      item_id = 'wbody|WEAPON_MACHETE',

      business_name = 'Beechwood Disciples',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})
