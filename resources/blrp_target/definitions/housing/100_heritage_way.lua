local address = '100 Heritage Way'
AddBoxZoneAutoname(vector4(-883.29, -436.75, 39.6, 27), 8.8, 0.6, {
  minZ = 38.6,
  maxZ = 41.0
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'HER-100',
      instance = 'Floor-1',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'HER-100',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 3rd Floor',

      id = 'HER-100',
      instance = 'Floor-3',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 4th Floor',

      id = 'HER-100',
      instance = 'Floor-4',
    },
  },

  distance = 2.5,
})

-- Floors

local initial_coords = vector4(-887.9089, -439.2651, 56.41931, 295)
local initial_min = 54.85
local initial_max = 57.45

for i = 0, 3 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 310), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end
