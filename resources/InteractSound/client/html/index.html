<html>
<head>
    <!-- Need to include jQuery! -->
    <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.1.1/howler.min.js" type="text/javascript"></script>
    <script>
      var audioPlayers = {}; // Store multiple sounds

      // Listen for NUI Messages.
      window.addEventListener('message', function(event) {
        if (event.data.transactionType == "playSound") {
          let soundId = event.data.transactionFile; // Unique ID for each sound

          // If sound is already playing, stop it before replaying
          if (audioPlayers[soundId]) {
            audioPlayers[soundId].stop();
          }

          // Create a new Howl instance for this sound
          audioPlayers[soundId] = new Howl({
            src: ["./sounds/" + soundId + ".ogg"],
            volume: event.data.transactionVolume
          });

          audioPlayers[soundId].play();
        }

        // Stop a specific sound
        if (event.data.transactionType == 'stopSound' && event.data.transactionFile) {
          let soundId = event.data.transactionFile;
          if (audioPlayers[soundId]) {
            audioPlayers[soundId].stop();
            delete audioPlayers[soundId]; // Remove from object
          }
        }
      });
    </script>
</head>
</html>
