pBasicNeeds = P.getInstance('blrp_core', 'basicneeds')

local character_selected = false

RegisterNetEvent('core:client:registerSelectedPlayer', function()
  character_selected = true
end)

Citizen.CreateThread(function()
  function tickNeeds()
    if
      not core.me().isHandcuffed() and
      not core.me().isInComa() and
      not core.me().get('god_active')
    then
      local thirst_variance = math.round(math.randomfloat(0.5, 0.7), 3)
      local hunger_variance = math.round(math.randomfloat(0.3, 0.5), 3)

      if core.me().hasGroup('Dispatch') then
        thirst_variance = 0.1
        hunger_variance = 0.1
      end

      pBasicNeeds.vary({ thirst_variance, hunger_variance })

      --[[
      -- Old system that was commented out when I ported this to core. TODO: implement one day?

      local ped = PlayerPedId()

      -- variations for one minute
      local vthirst = 0
      local vhunger = 0

      -- on foot, increase thirst/hunger in function of velocity
      if IsPedOnFoot(ped) then
        local vel = GetEntityVelocity(PlayerPedId())
        local factor = math.min(math.sqrt(vel.x * vel.x + vel.y * vel.y + vel.z * vel.z), 10)

        vthirst = vthirst + 1 * factor
        vhunger = vhunger + 0.5 * factor
      end

      -- in melee combat, increase
      if IsPedInMeleeCombat(ped) then
        vthirst = vthirst + 10
        vhunger = vhunger + 5
      end

      -- injured, hurt, increase
      if IsPedHurt(ped) or IsPedInjured(ped) then
        vthirst = vthirst + 2
        vhunger = vhunger + 1
      end
      ]]
    end

    SetTimeout(math.random(55, 65) * 1000, tickNeeds)
  end

  while not character_selected do
    Citizen.Wait(0)
  end

  Citizen.Wait(5000)

  tickNeeds()
end)
