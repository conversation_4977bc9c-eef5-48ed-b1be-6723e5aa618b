local function openPlayerSettings()
  local character = exports.blrp_core:me()
  local character_id = character.get('id')
  local isStaff = character.hasGroup('staff')

  local settings = {
    header = 'Player Settings',
    fields = {}
  }

  -- Vehicle Settings Header
  table.insert(settings.fields, {
    id = 'header_vehicle',
    txt = '── Vehicle Settings ──',
    disabled = true,
    grid_column_start = 'span 4',
  })

  -- Auto-unlock toggle
  table.insert(settings.fields, {
    id = 'auto_unlock_disabled',
    txt = 'Disable Auto-Unlock',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('auto-unlock-disabled') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- UI Seatbelt Flash (correct setting name: seatbelt-flash)
  table.insert(settings.fields, {
    id = 'ui_seatbelt_flash',
    txt = 'Enable Seatbelt Flash',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('UI-seatbelt-flash') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- Chat Settings Header
  table.insert(settings.fields, {
    id = 'header_chat',
    txt = '── Chat Settings ──',
    disabled = true,
    grid_column_start = 'span 4',
  })

  -- Mute All
  table.insert(settings.fields, {
    id = 'mute_all',
    txt = 'Mute All Chat',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('mute-all') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- Mute OOC
  table.insert(settings.fields, {
    id = 'mute_ooc',
    txt = 'Mute OOC Chat',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('ooc-muted') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- Mute Quit Messages
  table.insert(settings.fields, {
    id = 'mute_quit',
    txt = 'Mute Quit Messages',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('quit-muted') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- Mute Advertisements
  table.insert(settings.fields, {
    id = 'mute_ad',
    txt = 'Mute Advertisements',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('add-muted') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- UI Settings Header
  table.insert(settings.fields, {
    id = 'header_ui',
    txt = '── UI Settings ──',
    disabled = true,
    grid_column_start = 'span 4',
  })

  -- First Person Toggle
  table.insert(settings.fields, {
    id = 'fps_muted',
    txt = 'Disable Right-Click First Person',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('fps-muted') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  -- Housing Blips Toggle
  table.insert(settings.fields, {
    id = 'housing_blips_muted',
    txt = 'Hide Housing Blips',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('housing-blips-muted') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  table.insert(settings.fields, {
    id = 'header_radio',
    txt = '── Radio Settings ──',
    disabled = true,
    grid_column_start = 'span 4',
  })

  table.insert(settings.fields, {
    id = 'mic_clicks_enabled',
    txt = 'Radio Mic Clicks',
    options = { 'No', 'Yes' },
    value = exports.blrp_core:LocalStorageGetBool('mic_clicks_enabled') and 'Yes' or 'No',
    grid_column_start = 'span 2',
  })

  table.insert(settings.fields, {
    id = 'radio_sounds',
    txt = 'Radio Sounds',
    options = { 'Old', 'New' },
    value = exports.blrp_core:LocalStorageGet('radio_sounds'),
    grid_column_start = 'span 2',
  })

  table.insert(settings.fields, {
    id = 'mic_click_volume',
    txt = 'Mic Click Volume',
    options = { '25%', '50%', '75%', '100%', '125%', '150%' },
    value = exports.blrp_core:LocalStorageGet('mic_click_volume') or '100%',
    grid_column_start = 'span 2',
  })

  -- Staff-only settings
  if isStaff then
    table.insert(settings.fields, {
      id = 'header_staff',
      txt = '── Staff Settings ──',
      disabled = true,
      grid_column_start = 'span 4',
    })

    table.insert(settings.fields, {
      id = 'mute_staff',
      txt = 'Mute Staff Chat',
      options = { 'No', 'Yes' },
      value = exports.blrp_core:LocalStorageGetBool('staff-muted') and 'Yes' or 'No',
      grid_column_start = 'span 2',
    })
  end

  local response = exports.blrp_ui:TriggerFormWait(settings)

  if not response then
    return
  end

  -- Apply settings (convert dropdown values: "Yes" = true, "No" = false)
  if response.auto_unlock_disabled ~= nil then
    exports.blrp_core:LocalStorageSet('auto-unlock-disabled', response.auto_unlock_disabled == 'Yes')
  end

  if response.mute_all ~= nil then
    exports.blrp_core:LocalStorageSet('mute-all', response.mute_all == 'Yes')
  end

  if response.mute_ooc ~= nil then
    exports.blrp_core:LocalStorageSet('ooc-muted', response.mute_ooc == 'Yes')
  end

  if response.mute_quit ~= nil then
    exports.blrp_core:LocalStorageSet('quit-muted', response.mute_quit == 'Yes')
  end

  if response.mute_ad ~= nil then
    exports.blrp_core:LocalStorageSet('add-muted', response.mute_ad == 'Yes')
  end

  if response.fps_muted ~= nil then
    local fpsDisabled = response.fps_muted == 'Yes'
    exports.blrp_core:LocalStorageSet('fps-muted', fpsDisabled)
    TriggerEvent('customscripts:client:setFPSToggle', fpsDisabled)
  end

  if response.housing_blips_muted ~= nil then
    exports.blrp_core:LocalStorageSet('housing-blips-muted', response.housing_blips_muted == 'Yes')
  end

  if response.mic_clicks_enabled ~= nil then
    local micClicksEnabled = response.mic_clicks_enabled == 'Yes'
    exports.blrp_core:LocalStorageSet('mic_clicks_enabled', micClicksEnabled)
  end

  if response.radio_sounds ~= nil then
    exports.blrp_core:LocalStorageSet('radio_sounds', response.radio_sounds)
  end

  if response.mic_click_volume ~= nil then
    exports.blrp_core:LocalStorageSet('mic_click_volume', response.mic_click_volume)
  end

  if response.ui_seatbelt_flash ~= nil then
    local seatbeltFlash = response.ui_seatbelt_flash == 'Yes'
    exports.blrp_core:LocalStorageSet('UI-seatbelt-flash', seatbeltFlash)
    TriggerEvent('vrp:client:seatbeltFlashChanged', seatbeltFlash)
  end

  if isStaff and response.mute_staff ~= nil then
    local staffMuted = response.mute_staff == 'Yes'
    exports.blrp_core:LocalStorageSet('staff-muted', staffMuted)
    -- Trigger server event for staff mute
    TriggerServerEvent('blrp_tablet:server:muteStaff', staffMuted)
  end

  -- Show confirmation message
  TriggerEvent('chat:addMessage', {
    template = '<div class="chat-bubble" style="background-color: rgba(0, 230, 115, 0.6);"><i class="fas fa-check-circle"></i> {0}</div>',
    args = { "Player settings updated successfully!" }
  })
end

-- Register command
RegisterCommand('playersettings', function()
  openPlayerSettings()
end)

-- Export for menu system
exports('openPlayerSettings', openPlayerSettings)
