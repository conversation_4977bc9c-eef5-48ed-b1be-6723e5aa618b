pCharacter = {}
P.bindInstance('character', pCharacter)

pCharacter.hasPermission = function(_source, permission)
  return core.character(_source).hasPermissionPromise(permission)
end

local value_casts = {
  number = {
    ['rank_lspd'] = true,
    ['rank_bcso'] = true,
    ['rank_sasp'] = true,
    ['rank_lsfd'] = true,
    ['rank_doc'] = true,
    ['rank_doj'] = true,
    ['rank_lawyer'] = true,
    ['rank_lifer'] = true,
  }
}

function Trace()
  local trace = Citizen.InvokeNative(`FORMAT_STACK_TRACE` & 0xFFFFFFFF, nil, 0, Citizen.ResultAsString())

  if not trace then
    print('No trace available')
    return
  end

  trace = string.gsub(trace, 'ocal Tools.+return Tunnel%c+', '@vrp/panopticon/sv_pano_tunnel.lua')
  print(trace)
end

core.character = function(_source, suppress_no_source_error)
  _source = tonumber(_source)

  if not _source then
    if not suppress_no_source_error then
      print('[ERROR] No _source provided on character call. Breaking call')
      Trace()
    end

    return false
  end

  return {
    source = _source,

    trace = Trace,

    debug = function(...)
      if not GlobalState.is_dev and not characters[_source].groups['staff'] then
        return
      end

      print('[DEBUG] -> [' .. _source .. ']', ...)
      TriggerClientEvent('blrp_core:client:debugPrint', _source, ...)
    end,

    print_r = function(...)
      if not GlobalState.is_dev and not characters[_source].groups['staff'] then
        return
      end

      print('[DEBUG] -> [' .. _source .. ']', ...)
      print_r(...)
      TriggerClientEvent('blrp_core:client:debugPrintR', _source, ...)
    end,

    init = function(data)
      characters[_source] = data
      core.character(_source).sync()
    end,

    setUserData = function(key, value, deferred, json_encode)
      DataStore.storeUser(characters[_source].identifier, 'core:' .. characters[_source].id .. ':' .. key, value, deferred, json_encode)

      return value
    end,

    getUserData = function(key, force_retrieve, json_decode, coerce_type, default_value)
      local value = DataStore.getUser(characters[_source].identifier, 'core:' .. characters[_source].id .. ':' .. key, force_retrieve, json_decode)

      if value and coerce_type == 'vector' and type(value) == 'table' and value.x and value.y then
        if value.w and value.z then
          return vector4(value.x, value.y, value.z, value.w)
        end

        if value.z then
          return vector3(value.x, value.y, value.z)
        end

        return vector2(value.x, value.y)
      end

      if value and coerce_type == 'number' then
        return tonumber(value)
      end

      if value and coerce_type == 'boolean' then
        -- Needed because if the value was set in runtime it'll be a bool
        -- Only retrieved as a number from the database
        if type(value) == 'boolean' then
          return value
        end

        return tonumber(value) == 1
      end

      if not value and default_value then
        return default_value
      end

      return value
    end,

    getRawUserData = function(key, force_retrieve)
      return DataStore.getUser(characters[_source].identifier, key, force_retrieve)
    end,

    incrementUserData = function(key, amount)
      return DataStore.incrementUser(characters[_source].identifier, key, amount, true)
    end,

    set = function(key, value)
      if not characters[_source] then
        return
      end

      if value_casts.number[key] then
        value = tonumber(value)
      end

      characters[_source][key] = value

      TriggerEvent('core:server:characterPropertySet', _source, characters[_source]['identifier'], characters[_source]['id'], key, value)

      if key == 'dispatch_status' and characters[_source] and characters[_source].groups and characters[_source].groups.LSFD then
        GlobalState.available_ems = getComputedAvailableDispatchModality('EMS')
      end

      if key == 'groups' then
        characters[_source].permissions = core.computePermissions(value)
      end

      core.character(_source).sync()

      return characters[_source][key]
    end,

    get = function(key)
      if not characters[_source] then
        return false
      end

      if not key then return characters[_source] end

      if key == 'personalbanknumber' then
        return tonumber(characters[_source]['id']) + 100000
      end

      if key == 'fullname' then
        return characters[_source]['firstname'] .. ' ' .. characters[_source]['lastname']
      end

      if key == 'tablet_profile' then
        local p = promise.new()

        Citizen.CreateThread(function()
          local character_info = exports.blrp_tablet:DoPostRequest('/secure/info/' .. characters[_source]['id'])
          p:resolve(character_info)
        end)

        return Citizen.Await(p)
      end

      if key == 'staff_name' then
        local user_info = MySQL.Sync.fetchAll('SELECT id, staffName FROM vrp_users WHERE id = @vrp_id', {
            vrp_id = characters[_source]['identifier']
        })[1]

        return user_info.staffName
      end

      if key == 'age_hours' then
        local created_at = characters[_source].created_at

        if not created_at then
          return 0
        end

        created_at = math.floor(tonumber(created_at) / 1000)

        local diff = os.time() - created_at

        return math.floor(diff / 60 / 60)
      end

      local value = characters[_source][key]

      if value_casts.number[key] then
        value = tonumber(value)
      end

      return value
    end,

    --------------------------------
    ------------- TEBEX ------------
    --------------------------------

    loadTebexPurchases = function()
      local user_id = characters[_source]['identifier']

      if not user_id then
        return
      end

      local result = MySQL.query.await([[
        SELECT package_id FROM tebex_purchases
          WHERE status = 0 AND user_id = ?
      ]], { user_id })

      local tebex_purchases = {}

      for _, v in pairs(result) do
        local package_id = tonumber(v.package_id)

        if not tebex_purchases[package_id] then
          tebex_purchases[package_id] = 0
        end

        tebex_purchases[package_id] = tebex_purchases[package_id] + 1
      end

      core.character(_source).set('tebex_purchases', tebex_purchases)
    end,

    hasTebexPackage = function(package_id)
      local tebex_purchases = characters[_source]['tebex_purchases'] or {}

      return tebex_purchases[package_id] or false
    end,

    --------------------------------
    ---------- KICK / BAN ----------
    --------------------------------

    kick = function(reason)
      DropPlayer(_source, reason)
    end,

    ban = function(reason, admin_id, note, log_reason)
      MySQL.Async.execute('UPDATE vrp_users SET banned = true, ban_reason = @reason, banned_by_admin_id = @admin_id WHERE id = @user_id', {
        reason = reason,
        admin_id = admin_id,
        user_id = characters[_source]['identifier']
      })

      if log_reason then
        MySQL.Async.execute('INSERT INTO vrp_user_notes (user_id, added_by, note) VALUES (@user_id, @added_by, @note)', {
          user_id = characters[_source]['identifier'],
      		added_by = admin_id,
      		note = reason
      	})
      end

      if note then
        MySQL.Async.execute('INSERT INTO vrp_user_notes (user_id, added_by, note) VALUES (@user_id, @added_by, @note)', {
          user_id = characters[_source]['identifier'],
      		added_by = admin_id,
      		note = note
      	})
      end

      core.character(_source).kick('[Banned] ' .. reason)
    end,

    getStringIdentifier = function()
      return characters[_source]['firstname'] .. ' ' .. characters[_source]['lastname'] .. ' (' .. characters[_source]['identifier'] .. '-' .. characters[_source]['id'] .. ')'
    end,

    getStringIdentifierVrpOnly = function()
      return characters[_source]['firstname'] .. ' ' .. characters[_source]['lastname'] .. ' (' .. characters[_source]['identifier'] .. ')'
    end,

    getFullName = function()
      return characters[_source]['firstname'] .. ' ' .. characters[_source]['lastname']
    end,

    getGroups = function()
      if not characters[_source] or not characters[_source].groups then return false end
      return characters[_source].groups
    end,

    hasOrInheritsGroup = function(group_name)
      if not characters[_source] or not characters[_source].groups then
        return false
      end

      return computeAllGroups(characters[_source].groups)[group_name]
    end,

    hasGroup = function(groupName)
      if not characters[_source] or not characters[_source].groups then
        return false
      end

      if type(groupName) == 'table' then
        for _, _groupName in pairs(groupName) do
          if characters[_source].groups[_groupName] then
            return true
          end
        end
      elseif characters[_source].groups[groupName] then
        return true
      end

      return false
    end,

    addGroup = function(group_name)
      TriggerEvent('vrp:server:addUserGroup', tonumber(characters[_source]['identifier']), group_name)
    end,

    removeGroup = function(group_name)
      TriggerEvent('vrp:server:removeUserGroup', tonumber(characters[_source]['identifier']), group_name)
    end,

    getDealership = function()
      if not characters[_source] or not characters[_source].groups then
        return nil
      end

      for _, business_definition in pairs(businesses_custom) do
        local group_name = business_definition.name

        if business_definition.is_dealership and characters[_source].groups[group_name] then
          return group_name
        end
      end

      return nil
    end,

    isHouseDesigner = function()
      if not characters[_source] or not characters[_source].groups then
        return nil
      end

      for _, business_definition in pairs(businesses_custom) do
        local group_name = business_definition.name

        if
          business_definition.house_designer_permission and
          characters[_source].groups[group_name] and
          checkUserCanAccess(core.character(_source), business_definition.id, business_definition.house_designer_permission)
        then
          return group_name
        end
      end

      return false
    end,

    getBusinesses = function(perms, ids)
      if not characters[_source] or not characters[_source].groups then
        return nil
      end

      if type(perms) == 'string' then
        perms = { perms }
      end

      local businesses = {}

      for _, business_definition in pairs(businesses_custom) do
        local insert = false
        local business_name = business_definition.name
        local business_id = business_definition.id

        if characters[_source].groups[business_name] then
          if perms then
            for _, perm in pairs(perms) do
              if businessCanAccess(_source, business_name, perm) then
                insert = true
              end
            end
          end

          if not perms or insert then
            if ids then
              table.insert(businesses, business_id)
            else
              table.insert(businesses, business_name)
            end
          end
        end
      end

      return businesses
    end,

    getBusinessesByType = function(type)
        if not characters[_source] or not characters[_source].groups then
          return nil
        end
        local businesses = { }
        local _character = characters[_source]

        if type then
            -- Fetch businesses based on type
            businesses = MySQL.query.await('SELECT c.business_id, b.name FROM core_character_business c JOIN businesses b ON b.id = c.business_id WHERE (c.character_id = ? OR b.character_id = ?) AND b.type = ?', {
                _character['id'], _character['id'], type
            })
        else
            -- Fetch all businesses
            businesses = MySQL.query.await('SELECT c.business_id, b.name FROM core_character_business c JOIN businesses b ON b.id = c.business_id WHERE (c.character_id = ? OR b.character_id = ?)', {
                _character['id'], _character['id']
            })
        end

        return businesses
    end,


    getVehicleOwningBusinesses = function()
      local vehicle_businesses = {}

      if not characters[_source] or not characters[_source].groups then
        return vehicle_businesses
      end

      for _, business_definition in pairs(businesses_custom) do
        local group_name = business_definition.name

        if
          characters[_source].groups[group_name] and
          (
            business_definition.is_dealership or
            (
              business_definition.can_own_vehicles and
              businessCanAccess(_source, group_name, 'veh_use')
            )
          )
        then
          table.insert(vehicle_businesses, group_name)
        end
      end

      return vehicle_businesses
    end,

    -- VRP permission hooks

    hasPermissionPromise = function(name)
      local promise = promise:new()

      TriggerEvent('vrp:server:hasPermission', _source, name, function(has_permission)
        promise:resolve(has_permission)
      end)

      return Citizen.Await(promise)
    end,

    getPermissions = function()
      return core.getPermissions(_source, '.*')
    end,

    hasPermission = function(name, callback)
      TriggerEvent('vrp:server:hasPermission', _source, name, callback)
    end,

    hasPermissions = function(nodes, callback)
      local has_nodes = {}

      for _, node in ipairs(nodes) do
        has_nodes[node] = core.hasPermission(_source, node)
      end

      callback(has_nodes)
    end,

    -- Core permission hooks

    hasPermissionCore = function(node)
      return core.hasPermission(_source, node)
    end,

    hasPermissionsCore = function(nodes)
      local has_nodes = {}

      for _, node in ipairs(nodes) do
        has_nodes[node] = core.hasPermission(_source, node)
      end

      return has_nodes
    end,

    hasAnyPermissionCore = function(nodes)
      for _, node in ipairs(nodes) do
        if core.hasPermission(_source, node) then
          return true
        end
      end

      return false
    end,

    -- End permission hooks

    sync = function()
      TriggerClientEvent('blrp_core:client:registerCharacter', _source, characters[_source])
    end,
    notify = function(message, shouldSound)
      if not shouldSound then shouldSound = false end
      TriggerClientEvent('vrp:client:notify', _source, message, shouldSound)
    end,
    notifyError = function(message, duration)
      if not duration then duration = 5000 end

      TriggerClientEvent('mythic_notify:client:SendAlert', _source, {
        type = "error",
        text = message,
        length = duration
      })
    end,
    notifyDuration = function(message, duration)
      if not duration then duration = 5000 end

      TriggerClientEvent('mythic_notify:client:SendAlert', _source, {
        type = "inform",
        text = message,
        length = duration
      })
    end,
    notifyNew = function(message, duration, type)
      if not duration then duration = 5000 end
      if not type then type = 'inform' end

      TriggerClientEvent('mythic_notify:client:SendAlert', _source, {
        type = type,
        text = message,
        length = duration
      })
    end,
    playSoundAround = function(range, name, volume)
      if not volume then volume = 0.07 end
      TriggerClientEvent('InteractSound_CL:PlayWithinDistance', -1, _source, range, name, volume)
    end,
    varyHealth = function(amount)
      TriggerClientEvent('vrp:survival:varyHealth', _source, amount)
    end,
    varyHealthOverTime = function(amount, seconds)
      TriggerClientEvent('vrp:survival:varyHealthOverTime', _source, amount, seconds)
    end,
    addAddiction = function(drug)
      exports.vrp:AddAddiction(_source, drug)
    end,
    targetClosePlayer = function(distance, callback)
      local _promise = nil

      if not callback then
        _promise = promise:new()
        callback = function(target_source)
          _promise:resolve(target_source)
        end
      end

      TriggerEvent('core:server:selectNearestPlayer', _source, distance, callback)

      if _promise then
        return Citizen.Await(_promise)
      end
    end,
    targetCloseSurrenderedPlayer = function(distance)
      local _character = core.character(_source)

      local target__source = _character.targetClosePlayer(distance)

      if not target__source or target__source <= 0 then
        return
      end

      if not tSurvival.isPlayerSurrendered(target__source) then
        return
      end

      return target__source
    end,
    getJobName = function()
      local job_groups = exports.vrp:GetJobGroups()

      for group_name, _ in pairs(characters[_source].groups or {}) do
        if job_groups[group_name] then
          local name = job_groups[group_name].name

          if name ~= 'Unemployed' then
            return name
          end
        end
      end

      return 'Citizen'
    end,
    getRankName = function()
      local character_groups = characters[_source].groups or {}

      local job_groups = exports.vrp:GetJobGroups()
      local rank_name = ''

      for group_name, _ in pairs(character_groups) do
        local job_group = job_groups[group_name]

        if job_group then
          for rank_group_name, _rank_name in pairs(job_group.ranks) do
            if character_groups[rank_group_name] then
              rank_name = _rank_name
            end
          end

          return rank_name
        end
      end

      return rank_name
    end,
    animate = function(seq, upperOnly, looping)
      if upperOnly == nil then upperOnly = true end
      if looping == nil then looping = false end

      TriggerClientEvent('vrp:client:playAnimation', _source, upperOnly, seq, looping)
    end,
    stopAnimation = function(upperOnly)
      TriggerClientEvent('vrp:client:stopAnimation', _source, upperOnly)
    end,
    tryFingerPrintGame = function(levels, lives, minutes, callback)
      TriggerEvent('core:server:newDoFingerPrintGame', _source, levels, lives, minutes, callback)
    end,
    log = function(log_type, message, context)
      logSplunk(_source, log_type, message, context)
    end,
    drawInteract = function(interact)
      if not interact.hotkey then interact.hotkey = 'E' end
      if not interact.distance then interact.distance = 1 end
      if not interact.lang then interact.lang = 'Press {key} to use' end
      TriggerClientEvent('core:client:registerInteract', _source, interact)
    end,
    drawInteracts = function(interacts)
      for _, interact in pairs(interacts) do
        if not interact.args then interact.args = false end
        if not interact.hotkey then interact.hotkey = 'E' end
        if not interact.distance then interact.distance = 1 end
        if not interact.lang then interact.lang = 'Press {key} to use' end

        TriggerClientEvent('core:client:registerInteract', _source, interact)
      end
    end,
    removeInteract = function(interact)
      TriggerClientEvent('core:client:removeInteract', _source, interact)
    end,
    removeInteracts = function(interacts)
      for _, interact in pairs(interacts) do
        TriggerClientEvent('core:client:removeInteract', _source, interact)
      end
    end,
    drawMarker = function(marker_config)
      core.character(_source).client('blrp_core:client:interacts:registerMarker', marker_config)
    end,
    removeMarker = function(marker_config)
      core.character(_source).client('blrp_core:client:interacts:removeMarker', marker_config)
    end,
    getPosition = function(callback)
      TriggerEvent('vrp:server:getPlayerPosition', _source, function(position)
        callback(position)
      end)
    end,
    meText = function(text)
      TriggerClientEvent("3dme:triggerDisplay", -1, text, _source)
      TriggerEvent('rcore_cam:me', _source, text)
    end,
    dpEmote = function(name)
      TriggerClientEvent("dpemotes:EmoteCommandStart", _source, name, "")
    end,
    client = function(event_name, ...)
      TriggerClientEvent(event_name, _source, ...)
    end,
    request = function(text, duration)
      if not duration then
        duration = 15
      end

      local _promise = promise:new()

      TriggerEvent('vrp:server:request', _source, text, duration, function(accepted_player, accepted)
        _promise:resolve(accepted)
      end)

      return Citizen.Await(_promise)
    end,
    hideInventory = function(synchronous)
      if synchronous then
        tInventory.hide(_source)
      else
        TriggerClientEvent('blrp_inventory:hide', _source)
      end
    end,
    progress = function(label, seconds, skill_forward, callback, animation, prop)
      if prop and prop.model then
        CoreAnticheat.EntityListener.AddAllowedModel(prop.model)
        --acAddAllowedModel(prop.model)
      end

      local packet = {
        name = label .. tostring(seconds),
        duration = 1000 * seconds,
        label = label,
        useWhileDead = false,
        canCancel = false, -- Must be this way for now
        controlDisables = {
          disableMovement = true,
          disableCarMovement = true,
          disableMouse = false,
          disableCombat = true,
        },
        animation = animation,
        prop = prop
      }

      if skill_forward then packet.allow_skill_forward = skill_forward end

      exports['mythic_progbar']:serverProgress(_source, packet, function(cancelled)
        if not cancelled then
          callback(true)
        end
      end)
    end,
    progressCustom = function(label, seconds, callback, options)
      if not options then options = {} end

      if not options.canCancel then options.canCancel = false end

      if not options.controlDisables then
        options.controlDisables = {
          disableMovement = true,
          disableCarMovement = true,
          disableMouse = false,
          disableCombat = true,
        }
      end

      if options.prop and options.prop.model then
        CoreAnticheat.EntityListener.AddAllowedModel(options.prop.model)
        --acAddAllowedModel(options.prop.model)
      end

      local packet = {
        name = label .. tostring(seconds),
        duration = 1000 * seconds,
        label = label,
        useWhileDead = false,
        canCancel = options.canCancel,
        controlDisables = options.controlDisables,
        animation = options.animation,
        prop = options.prop,
        extra = options.extra,
      }

      if options.skill_forward then packet.allow_skill_forward = options.skill_forward end
      if options.interrupt_when_shot then packet.interrupt_when_shot = options.interrupt_when_shot end
      if options.interrupt_when_ragdoll then packet.interrupt_when_ragdoll = options.interrupt_when_ragdoll end

      exports['mythic_progbar']:serverProgress(_source, packet, function(cancelled)
        callback(cancelled)
      end)
    end,
    progressPromise = function(label, seconds, options)
      local _p = promise:new()

      core.character(_source).progressCustom(label, seconds, function(cancelled)
        _p:resolve(not cancelled)
      end, options)

      return Citizen.Await(_p)
    end,
    prompt = function(title, default_text, strip_extended_characters, max_length)
      if not default_text then
        default_text = ''
      end

      local result = exports.vrp:prompt(_source, title, default_text)

      scanInputForBadWords(_source, 'prompt', result)

      if strip_extended_characters then
        result = str_strip_extended_characters(result)
      end

      if max_length and string.len(result) > max_length then
        core.character(_source).notify('Maximum length is ' .. max_length)
        return false
      end

      return result
    end,
    canUnlockMoneyBag = function()
      local money_bag_types = {
        'money_bag',
        'money_bag2',
        'money_bag_bcsb',
        'money_bag_bcsb2',
        'money_bag_cb',
      }

      if tSurvival.getIsInAnyVehicle(_source) then
        return false
      end

      local player_coords = GetEntityCoords(GetPlayerPed(_source))

      local banks_coords = {
        vector2(249.100, 217.726), -- Central Bank
        vector2(-106.371, 6467.151), -- BCSB
        vector2(1176.211, 2709.419), -- Grand Senora Desert
        vector2(-2960.064, 481.629), -- Banham Canyon
        vector2(-352.963, -51.934), -- Burton
        vector2(-1212.540, -333.590), -- Rockford Hills
        vector2(312.250, -281.133), -- Alta
        vector2(147.837, -1042.800), -- Pillbox
      }

      local near_bank = false

      for _, bank_coords in pairs(banks_coords) do
        if #(player_coords.xy - bank_coords) < 100.0 then
          near_bank = true
        end
      end

      if near_bank then
        return false
      end

      for _, bag_item_id in ipairs(money_bag_types) do
        if core.character(_source).hasItemQuantity(bag_item_id, 1, false, true) then
          return true
        end
      end

      return false
    end,
    tryUnlockMoneyBag = function(from_advanced_lockpick, fail)
      if from_advanced_lockpick == nil then
        from_advanced_lockpick = false
      end

      local _character = core.character(_source)

      local money_bag_types = {
        ['money_bag'] = math.random(10200, 17400),
        ['money_bag2'] = 0, -- New fleeca bag
        ['money_bag_bcsb'] = math.random(20150, 26650),
        ['money_bag_bcsb2'] = 0,
        ['money_bag_cb'] = math.random(60500, 86900),
        ['grenade_box'] = math.random(4, 8),
      }

      local found_bag = false

      local player_coords = GetEntityCoords(GetPlayerPed(_character.source))

      local banks_coords = {
        vector2(249.100, 217.726), -- Central Bank
        vector2(-106.371, 6467.151), -- BCSB
        vector2(1176.211, 2709.419), -- Grand Senora Desert
        vector2(-2960.064, 481.629), -- Banham Canyon
        vector2(-352.963, -51.934), -- Burton
        vector2(-1212.540, -333.590), -- Rockford Hills
        vector2(312.250, -281.133), -- Alta
        vector2(147.837, -1042.800), -- Pillbox
      }

      local near_bank = false

      for _, bank_coords in pairs(banks_coords) do
        if #(player_coords.xy - bank_coords) < 100.0 then
          near_bank = true
        end
      end

      if near_bank then
        return
      end

      for bag_item_id, bag_payout in pairs(money_bag_types) do
        local actual_payout = bag_payout
        local bank_name = "Fleeca"
        if not found_bag and _character.hasItemQuantity(bag_item_id, 1, false, true) then
          if from_advanced_lockpick and not tLockpick.lockpick(_character.source, { 3, 'number' }) then
            if math.random(1, 4) > 1 then
              _character.notify('You break the lockpick while fiddling around')
              _character.take('lockpick_adv', 1)
            end

            _character.notify('Pick failed')

            return
          end

          -- fleeca new money bag
          if bag_item_id == 'money_bag2' then
            bank_name = "Fleeca"
            local meta, item_id_meta = _character.hasGetItemMeta(bag_item_id, true)

            bag_item_id = item_id_meta

            if not meta or not meta.bag_amount then
              return
            end

            actual_payout = meta.bag_amount
          end

          -- BCSB new money bag
          if bag_item_id == 'money_bag_bcsb2' then
            bank_name = "BCSB"
            local meta, item_id_meta = _character.hasGetItemMeta(bag_item_id, true)

            bag_item_id = item_id_meta

            if not meta or not meta.bag_amount then
              return
            end

            actual_payout = meta.bag_amount
          end
          if bag_item_id == 'money_bag_bcsb' then
            bank_name = "BCSB"
          end
          if bag_item_id == 'money_bag_cb' then
            bank_name = "Pacific S"
          end

          if not _character.take(bag_item_id, 1, false) then
            return
          end

          found_bag = true

          if bag_item_id == 'grenade_box' then
            if not _character.progressPromise('Drilling Padlock', 60, {
              animation = {
                animDict = 'amb@prop_human_parking_meter@female@base',
                anim = 'base_female',
                flags = 49
              }
            }) then
              return
            end

            _character.log('ACTION', 'Unlocked Grenade Box / item_id = ' .. bag_item_id .. ' / grenades = ' .. actual_payout .. ' / advanced_lockpick = ' .. tostring(from_advanced_lockpick))
            _character.animate({ {'mp_common', 'givetake1_a'} }, true)
            _character.give('wbody|WEAPON_GRENADE', actual_payout)
          else
            if from_advanced_lockpick then
              if tSurvival.getIsInAnyVehicle(_character.source) then
                _character.notify('The vehicle is shaking too much to pick the bag effectively')
                return
              end

              if not _character.progressPromise('Unlocking Money Bag', 10, {
                animation = {
                  animDict = 'amb@prop_human_parking_meter@female@base',
                  anim = 'base_female',
                  flags = 49
                }
              }) then
                return
              end
            end

            if fail then
              _character.log('ACTION', 'Attempted to unlock money bag, failed / item_id = ' .. bag_item_id)
              _character.animate({ {'mp_common', 'givetake1_a'} }, true)
              _character.notify('You unlocked the money bag but it was stuffed with socks')

              return
            end

            _character.log('ACTION', 'Unlocked money bag / item_id = ' .. bag_item_id .. ' / cash = ' .. actual_payout .. ' / advanced_lockpick = ' .. tostring(from_advanced_lockpick))
            _character.animate({ {'mp_common', 'givetake1_a'} }, true)

            local serial = string.random(15, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')

            _character.give('bank_moneybag', 1, {
              bag_serial = serial,
              evidence_bin_label = 'Serial: ' .. serial,
              storage_label = 'Opened ' .. bank_name .. ' Bag'
            })

            core.chest('bank:moneybag:' .. serial).setContents({
              ['cash'] = {
                amount = actual_payout
              }
            })

            _character.openChest('bank:moneybag:' .. serial, 0.1, false, {
              max_slots = 1
            })
          end
        end
      end
    end,

    --------------------------------
    --------- NATIVE / RPC ---------
    --------------------------------

    getCoordinates = function()
      return GetEntityCoords(GetPlayerPed(_source))
    end,

    getGrid = function(scale)
      return GetGridAtCoords(GetEntityCoords(GetPlayerPed(_source)), scale)
    end,

    getHeading = function()
      return GetEntityHeading(GetPlayerPed(_source))
    end,

    distanceFrom = function(_vector3)
      return #(GetEntityCoords(GetPlayerPed(_source)) - _vector3)
    end,

    peopleAroundMe = function(raduis)
      local my_pos = (GetEntityCoords(GetPlayerPed(_source)))
      local found__sources = { }

      for __source, __ in pairs(characters) do
        local distanceFromMe = #(GetEntityCoords(GetPlayerPed(__source)) - my_pos)
        if (distanceFromMe) <= raduis then
          table.insert(found__sources, __source)
        end
      end

      return found__sources
    end,

    setCoordinates = function(coords, heading)
      tSurvival.teleportCoords(_source, { coords, heading })
    end,

    getBucket = function()
      return tonumber(GetPlayerRoutingBucket(tostring(_source)))
    end,

    getInstance = function()
      local bucket = tonumber(GetPlayerRoutingBucket(tostring(_source)))
      local instance_info = getInstanceInformation(bucket)

      if instance_info then
        return instance_info.name
      end

      return bucket == 0 and 'global' or 'unknown'
    end,

    getVehicle = function(lastVehicle)
      if lastVehicle == nil then
        lastVehicle = false
      end

      local vehicle = GetVehiclePedIsIn(GetPlayerPed(_source), lastVehicle)

      if not vehicle or vehicle <= 0 then
        return nil
      end

      return vehicle
    end,

    getHeading = function()
      return GetEntityHeading(GetPlayerPed(_source))
    end,

    -------------------------------
    -------- MONEY RELATED --------
    -------------------------------

    tryPayment = function(amount, notify, allow_bank, allow_cash, allow_dirty_cash, currency)
      local cash_amount_paid = 0
      local bank_amount_paid = 0

      if not currency then
        currency = 'cash'
      end

      if currency ~= 'cash' then
        allow_bank = false
        allow_dirty_cash = false
      end

      if amount == 0 then
        return true
      end

      local _character = core.character(_source)

      if amount < 0 then
        local ban_info = 'Attempted to make a negative payment / amount = ' .. amount

        _character.ban('Scripting perm (anticheat)', 0, ban_info, true)
        _character.log('ANTICHEAT', ban_info .. ' / trace = ' .. Citizen.InvokeNative(`FORMAT_STACK_TRACE` & 0xFFFFFFFF, nil, 0, Citizen.ResultAsString()))
        return false
      end

      if notify == nil then
        notify = true
      end

      if allow_cash == nil then
        allow_cash = true
      end

      if allow_bank == nil then
        allow_bank = true
      end

      if not allow_cash and not allow_bank then
        allow_cash = true
      end

      local paid = false
      local method = 'cash'

      local cash_amount = _character.getItemQuantity(currency)

      -- Attempt 1: take dirty money
      if not paid and allow_dirty_cash then
        local dirty_cash_amount = _character.getItemQuantity('cash_dirty')

        if dirty_cash_amount >= amount then
          paid = _character.take('cash_dirty', amount, false)

          if paid then
            _character.log('MONEY', 'tryPayment / method dirty cash / amount = ' .. amount)
          end
        end
      end

      -- Attempt 2: take physical cash
      if not paid and allow_cash and cash_amount >= amount then
        paid = _character.take(currency, amount, false)
        cash_amount_paid = amount

        if paid then
          _character.log('MONEY', 'tryPayment / method cash / amount = ' .. amount)
        end
      end

      -- Attempt 3: take bank money
      if not paid and allow_bank then
        if _character.get('assets_frozen') then
          _character.notifyError('Payment Declined: ACCOUNT FROZEN BY THE STATE')
          _character.log('MONEY', 'tryPayment / assets frozen, payment denied / amount = ' .. amount)
          return false
        end

        paid = _character.tryTakeBankMoney(amount)
        bank_amount_paid = amount

        if paid then
          method = 'from bank account'

          _character.log('MONEY', 'tryPayment / method bank / amount = ' .. amount)
        end
      end

      -- Attempt 4: compound
      if not paid and allow_cash and allow_bank then
        if (_character.getBankMoney() + cash_amount) >= amount then
          local from_bank = (amount - cash_amount)

          if from_bank > 0 and _character.get('assets_frozen') then
            _character.notifyError('Payment Declined: ACCOUNT FROZEN BY THE STATE')
            _character.log('MONEY', 'tryPayment / assets frozen, payment denied / amount = ' .. amount)
            return false
          end

          paid = _character.take('cash', cash_amount, false) and _character.tryTakeBankMoney(from_bank)
          cash_amount_paid = cash_amount
          bank_amount_paid = from_bank

          if paid then
            method = 'cash and from bank account'

            _character.log('MONEY', 'tryPayment / method compound / amount = ' .. amount .. ' / from cash = ' .. cash_amount .. ' / from bank = ' .. from_bank)
          end
        end
      end

      if notify then
        if paid then
          if ({
            ['cash_xm23_coin'] = true,
            ['c24_panth_hide'] = true,
            ['c24_panth_fang'] = true,
            ['c24_doubloon'] = true,
            ['cash_klepto'] = true,
            ['xm24_furvouch'] = true,
            ['arcade_ticket'] = true,
          })[currency] then
            method = ''
          end

          if currency == 'cash_xm23_coin' then
            method = ''
          end

          _character.notify('Charged ' .. currencySymbol(currency) .. amount .. ' ' .. method)
        else
          _character.notify('You do not have enough money')
        end
      end

      return paid, cash_amount_paid, bank_amount_paid
    end,

    getBankMoney = function()
      local rows = MySQL.Sync.fetchAll('SELECT id, balance FROM bank_accounts WHERE account_type = "Personal" AND owner_character_id = @char_id', {
        char_id = characters[_source].id
      })

      if #rows <= 0 then
        return 0
      end

      return rows[1].balance
    end,

    giveBankMoney = function(amount)
      if amount < 0 then
        return
      end

      local _character = core.character(_source)

      MySQL.Async.execute('UPDATE bank_accounts SET balance = balance + @amount WHERE account_number = @account_number', {
        account_number = _character.get('personalbanknumber'),
        amount = amount
      })

      _character.log('MONEY', 'giveBankMoney / amount = ' .. amount)
    end,

    takeBankMoney = function(amount)
      if amount < 0 then
        return
      end

      local _character = core.character(_source)

      MySQL.Async.execute('UPDATE bank_accounts SET balance = balance - @amount WHERE account_number = @account_number', {
        account_number = _character.get('personalbanknumber'),
        amount = amount
      })

      _character.log('MONEY', 'takeBankMoney / amount = ' .. amount)
    end,

    tryTakeBankMoney = function(amount)
      amount = tonumber(amount)

      if not amount or amount < 0 then
        return false
      end

      local _character = core.character(_source)

      local rows = MySQL.Sync.fetchAll('SELECT * FROM bank_accounts WHERE account_number = @account_number AND balance >= @amount', {
        account_number = _character.get('personalbanknumber'),
        amount = amount
      })

      if #rows <= 0 then
        return false
      end

      _character.takeBankMoney(amount)

      return true
    end,

    getCash = function()
      return core.character(_source).getItemQuantity('cash')
    end,

    hasCash = function(amount)
      if amount <= 0 then
        return true
      end

      return core.character(_source).hasItemQuantity('cash', amount)
    end,

    giveCash = function(amount, notify, currency)
      if not currency then
        currency = 'cash'
      end

      amount = math.floor(amount)

      local _character = core.character(_source)

      if notify or notify == nil then
        _character.notify('Received ' .. currencySymbol(currency) .. amount)
      end

      _character.give(currency, amount, false, false)
      _character.log('MONEY', 'giveCash', {
        amount = amount,
        currency = currency,
      })
    end,

    -------------------------------
    --------- BASIC NEEDS ---------
    -------------------------------

    getNeed = function(need)
      local basicneed_config = config_basicneeds[need]

      if not basicneed_config then
        return 0
      end

      local _character = core.character(_source)
      local needs_cached = _character.getUserData('needs', false, true)

      if not needs_cached then
        needs_cached = {}

        for _need, _ in pairs(config_basicneeds) do
          needs_cached[_need] = 0
        end

        _character.setUserData('needs', needs_cached, true, true)
      end

      return needs_cached[need] or 0
    end,

    setNeed = function(need, value)
      local basicneed_config = config_basicneeds[need]

      if not basicneed_config then
        return 0
      end

      local _character = core.character(_source)
      local needs_cached = _character.getUserData('needs', false, true)

      if not needs_cached then
        needs_cached = {}

        for _need, _ in pairs(config_basicneeds) do
          needs_cached[_need] = 0
        end

        _character.setUserData('needs', needs_cached, true, true)
      end

      value = math.clampFloat(value, 0, 100)

      needs_cached[need] = value
      _character.setUserData('needs', needs_cached, true, true)
      _character.set(need, value)

      return value
    end,

    varyNeed = function(need, variance)
      if variance == 0 then
        return 0
      end

      local basicneed_config = config_basicneeds[need]

      if not basicneed_config then
        return -variance
      end

      local _character = core.character(_source)
      local value = _character.getNeed(need) - variance

      local overflow_config = basicneed_config.overflow

      if value <= 0 and overflow_config then
        local overflow = -value

        if overflow_config.notify then
          _character.notify(overflow_config.notify)
        end

        if overflow_config.damage then
          local damage = overflow_config.damage(overflow)

          if damage ~= 0 then
            _character.varyHealth(damage)
            _character.log('ACTION', 'Incurred ' .. damage .. ' damage as a result of ' .. overflow_config.word)
          end
        end
      end

      if basicneed_config.cascade then
        basicneed_config.cascade(_character, need, variance)
      end

      value = math.clampFloat(value, 0, 100)

      _character.setNeed(need, value)

      return value
    end,

    varyNeedOverTime = function(need, variance, seconds)
      local basicneed_config = config_basicneeds[need]

      if not basicneed_config then
        return
      end

      Citizen.CreateThread(function()
        local _character = core.character(_source)
        local count = math.abs(variance)
        local pedLastValue = _character.getNeed(need)
        seconds = (seconds / math.abs(variance)) * 1000
        variance = variance / (math.abs(variance))

        while count >= 0 do
          local value = math.ceil(_character.getNeed(need))

          if pedLastValue <= value then
            local n = math.floor(value + variance)
            _character.setNeed(need, n)
            pedLastValue = n
          end

          count = count - 1
          Citizen.Wait(seconds)
        end
      end)
    end,

    -------------------------------
    ---------- APTITUDES ----------
    -------------------------------

    getAllAptitudes = function(vrpid,characterId)
        local pattern = string.format("core:%s:aptitude:%%", characterId)
        local results = DataStore.getUserBatchData(vrpid,"aptitude",{character_id = characterId})

        local aptitudes = {}

        for _, row in ipairs(results) do
            -- extract aptitude name from dkey
            if not row.dvalue == '__datastore_null' then
              local aptName = row.dkey:match("^core:" .. characterId .. ":aptitude:(.+)$")
              if aptName then
                  local apt_id = aptName
                  local apt_xp = row.dvalue
                  aptitudes[aptName] = tonumber(row.dvalue)
              end
            end
        end
        return aptitudes
    end,

    getAptitudeLevel = function(aptitude,xp_provided)
      local aptitude_config = config_aptitudes[aptitude]

      if not aptitude_config then
        return 1
      end

      if xp_provided then
        return aptitude_config.xptolevel(xp_provided)
      end

      return aptitude_config.xptolevel(core.character(_source).getAptitudeXp(aptitude))
    end,

    setAptitudeLevel = function(aptitude, level)
      local aptitude_config = config_aptitudes[aptitude]

      if not aptitude_config then
        return
      end

      local new_level = math.min(level, aptitude_config.max_level)

      local xp = aptitude_config.leveltoxp(new_level)

      core.character(_source).setAptitudeXp(aptitude, xp)

      return new_level
    end,

    getAptitudeXp = function(aptitude)
      local aptitude_config = config_aptitudes[aptitude]

      if not aptitude_config then
        return 0
      end

      local xp = tonumber(core.character(_source).getUserData('aptitude:' .. aptitude)) or 0

      return xp
    end,

    setAptitudeXp = function(aptitude, xp, silent)
      local aptitude_config = config_aptitudes[aptitude]

      if not aptitude_config then
        return 0
      end

      local _character = core.character(_source)

      local old_level = _character.getAptitudeLevel(aptitude)

      xp = math.max(0, math.min(xp, (aptitude_config.leveltoxp(aptitude_config.max_level) + 1)))

      local new_level = aptitude_config.xptolevel(xp)
      _character.setUserData('aptitude:' .. aptitude, xp, true)

      if not silent then
        if old_level > new_level then
          TriggerEvent('core:aptitudes:levelDown', _source, aptitude, math.floor(new_level), math.floor(old_level), xp)
        elseif old_level < new_level then
          TriggerEvent('core:aptitudes:levelUp', _source, aptitude, math.floor(new_level), math.floor(old_level), xp)
        end
      end

      return xp
    end,

    varyAptitudeXp = function(aptitude, amount)
      local _character = core.character(_source)

      return _character.setAptitudeXp(aptitude, _character.getAptitudeXp(aptitude) + amount)
    end,

    -------------------------------
    ---------- INVENTORY ----------
    -------------------------------

    clearInventory = function()
      DataStore.store('chest:inventory:' .. characters[_source].id, {}, true, true)
    end,

    getInventoryMaxWeight = function()
      local level = core.character(_source).getAptitudeLevel('strength')

      local weight_bonus = 0

      -- Testosterone addiction
      local dkey = 'vRP:addiction' .. characters[_source].id
      local addictions = DataStore.getUser(characters[_source].identifier, dkey, false, true)

      if addictions and addictions.bst and addictions.bst.concentration >= 400 then
        weight_bonus = weight_bonus + 5
      end

      return 30 + (10 * level) + weight_bonus
    end,

    getInventoryCurrentWeight = function()
      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      return GItems.getTotalWeight(inventory)
    end,

    getItemQuantity = function(item_id, meta_loose)
      if not item_id then
        return 0
      end

      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)
      local quantity = 0

      if meta_loose then
        for inventory_item_id, inventory_item_data in pairs(inventory) do
          if inventory_item_id == item_id or string.match(inventory_item_id, item_id .. ':meta:') then
            quantity = quantity + inventory_item_data.amount
          end
        end
      elseif inventory[item_id] then
        quantity = inventory[item_id].amount
      end

      return quantity
    end,

    hasItemFromCategory = function(categories, include_storages)
      if not include_storages then
        include_storages = false
      end

      if type(categories) == 'string' then
        categories = { categories }
      end

      local items = core.character(_source).getAllItems(include_storages)

      for item_id, _ in pairs(items) do
        local item_definition = GItems.getItemDefinition(item_id)

        if item_definition then
          for _, category in pairs(categories) do
            if item_definition.category == category then
              return item_id
            end

            if item_definition.categories_secondary then
              for _, _category in pairs(item_definition.categories_secondary) do
                if _category == category then
                  return item_id
                end
              end
            end
          end
        end
      end

      return false
    end,

    getItemsFromCategory = function(category, list)
      local items = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)
      local items_category = {}

      for item_id, item_data in pairs(items) do
        local item_definition = GItems.getItemDefinition(item_id)
        local valid = false

        if item_definition then
          if item_definition.category == category then
            valid = true
          end

          if item_definition.categories_secondary then
            for _, _category in pairs(item_definition.categories_secondary) do
              if _category == category then
                valid = true
              end
            end
          end
        end

        if valid then
          if list then
            table.insert(items_category, item_id)
          else
            items_category[item_id] = item_data.amount
          end
        end
      end

      return items_category
    end,

    hasItemQuantity = function(item_id, amount, notify_missing, meta_loose)
      local quantity = core.character(_source).getItemQuantity(item_id, meta_loose)
      local has_quantity = quantity >= amount

      if not has_quantity and notify_missing then
        core.character(_source).notify('Missing ' .. amount .. ' ' .. GItems.getItemName(item_id))
      end

      return has_quantity
    end,

    hasItemQuantities = function(items)
      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      local missing_items = {}

      for item_id, item_amount in pairs(items) do
        local missing_amount = 0

        if not inventory[item_id] then
          missing_amount = item_amount
        end

        if inventory[item_id] and inventory[item_id].amount < item_amount then
          missing_amount = item_amount - inventory[item_id].amount
        end

        if missing_amount > 0 then
          table.insert(missing_items, {
            name = item_id,
            amount = missing_amount
          })
        end
      end

      return (#missing_items == 0), missing_items
    end,

    hasAnyItems = function(items, meta_loose)
      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      for _, item_id in pairs(items) do
        if meta_loose then
          for inventory_item_id, inventory_item_data in pairs(inventory) do
            if inventory_item_id == item_id or string.match(inventory_item_id, item_id .. ':meta:') then
              return true
            end
          end
        elseif inventory[item_id] then
          return true
        end
      end

      return false
    end,

    hasRoomFor = function(item_id, amount, items_taking, meta)
      local _character = core.character(_source)
      local max_weight = _character.getInventoryMaxWeight()
      local items = _character.getAllItems(true)
      local before_weight = _character.getInventoryCurrentWeight()
      local taking_weight = 0
      local giving_weight = GItems.getItemWeight(item_id) * amount
      local item_definition = GItems.getItemDefinition(item_id)
      local item_amt = 0
      if meta and meta._w then
        giving_weight = giving_weight + meta._w
      end

      if items_taking then
        for taking_item_id, taking_item_amount in pairs(items_taking) do
          taking_weight = taking_weight + (GItems.getItemWeight(taking_item_id) * taking_item_amount)
        end
      end

      for itemid, item_data in pairs(items) do
        local base_inventory_item_id = itemid:match("^(.-):") or itemid
        local base_itemtaking_item_id = item_id:match("^(.-):") or item_id
        if base_inventory_item_id == base_itemtaking_item_id then
          item_amt = item_amt + item_data.amount
          if item_definition and item_definition.carry_limit then
            if item_data.amount + amount > item_definition.carry_limit then
              return false
            end
          end
        end
      end

      if item_definition and item_definition.carry_limit then
        if item_amt >= item_definition.carry_limit then
          return false
        end
      end

      return (before_weight - taking_weight + giving_weight) <= max_weight
    end,

    hasGetItemMeta = function(item_id, meta_loose)
      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      if meta_loose then
        for inventory_item_id, inventory_item_data in pairs(inventory) do
          if inventory_item_id == item_id or string.match(inventory_item_id, item_id .. ':meta:') then
            return (inventory_item_data.meta or {}), inventory_item_id
          end
        end
      elseif inventory[item_id] then
        return (inventory[item_id].meta or {}), item_id
      end

      return nil, nil
    end,

    hasGetItemMetaWithProperty = function(item_id, truth_test)
      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      for inventory_item_id, inventory_item_data in pairs(inventory) do
        local meta = inventory_item_data.meta or {}

        if
          string.match(inventory_item_id, item_id .. ':meta:') and
          truth_test(meta)
        then
          return meta, inventory_item_id
        end
      end

      return nil, nil
    end,

    getAllItems = function(include_carried_storages)
      local items = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      if include_carried_storages then
        for item_id, _ in pairs(items) do
          local item_definition, item_id_base = GItems.getItemDefinition(item_id)
          local cat_secondary = false
          local is_repairkit = false
          if item_definition and item_definition.categories_secondary then
            cat_secondary = array(item_definition.categories_secondary):find(function(v)
              return v == 'storage'
            end)
          end

          if item_definition and item_definition.category == 'storage' or item_definition and cat_secondary then
            local chest_name = string.gsub(item_id, ':meta', '')

            local contents = core.chest(chest_name).fetchContents()

            for contents_item_id, contents_item_data in pairs(contents) do
              if not items[contents_item_id] then
                items[contents_item_id] = {
                  amount = 0
                }
              end

              items[contents_item_id].amount = items[contents_item_id].amount + contents_item_data.amount
            end
          end
        end
      end

      return items
    end,

    dropNearby = function(item_id, amount, notify)
      if notify then
        core.character(_source).notify('Dropped ' .. amount .. ' ' .. GItems.getItemName(item_id) .. ' on the ground')
      end

      local player_bucket = Player(_source).state.bucket_name
      exports.blrp_inventory:DropGroundItem(core.character(_source).getCoordinates(), item_id, amount, nil, player_bucket)
    end,

    take = function(item_id, amount, notify, callback)
      if notify == nil then
        notify = true
      end

      amount = tonumber(amount)

      if not item_id or not amount or amount <= 0 then
        if callback then
          callback(false)
        end

        return false
      end

      local inventory = DataStore.get('chest:inventory:' .. characters[_source].id, false, true)

      local entry = inventory[item_id]

      if not entry then
        if callback then
          callback(false)
        end

        return false
      end

      if entry.amount < amount then
        local missing_amount = amount - entry.amount

        if notify then
          core.character(_source).notify('Missing ' .. missing_amount .. ' ' .. GItems.getItemName(item_id))
        end

        if callback then
          callback(false)
        end

        return false
      end

      entry.amount = entry.amount - amount

      if entry.amount <= 0 then
        inventory[item_id] = nil
      else
        inventory[item_id] = entry
      end

      local item_definition, item_id_metafree = GItems.getItemDefinition(item_id)
      if item_definition.client_aware then
        local clientAwareItems = core.character(_source).get('clientAwareItems') or {}
        clientAwareItems[item_id_metafree] = entry.amount
        core.character(_source).set('clientAwareItems', clientAwareItems)
      end

      DataStore.store('chest:inventory:' .. characters[_source].id, inventory, true, true)

      if notify then
        core.character(_source).notify('Removed ' .. amount .. ' ' .. GItems.getItemName(item_id, entry.meta))
      end
      handlePotentialWeaponTransfer(_source, false, item_id)
      syncVehicleRestrictedItems(_source)

      if callback then
        callback(true)
      end

      return true
    end,

    giveOrDrop = function(item_id, amount, meta, notify, _, callback, item_is_from_storage)
      local _character = core.character(_source)

      if not _character.hasRoomFor(item_id, amount, false, meta) then
        local item_definition, item_id_metafree = GItems.getItemDefinition(item_id)

        if item_id == item_id_metafree then
          -- Durability imprint for non-firearms
          if item_definition.item_durability then
            local time = os.time()
            local char_id = characters[_source].id

            meta = {
              dur_cur = item_definition.item_durability.dur_cur or item_definition.item_durability.dur_initial,
              dur_start = item_definition.item_durability.dur_initial,
            }

            if item_definition.unstackable then
              local data_key = 'core:unstackable:' .. item_id_metafree
              local counter = tonumber(DataStore.get(data_key) or 0)

              for i = 1, amount do
                counter = counter + 1

                local individual_item_id = item_id .. ':meta:' .. counter

                exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), individual_item_id, 1, meta)
              end

              DataStore.store(data_key, counter, true)
            else
              for i = 1, amount do
                local individual_item_id = item_id .. ':meta:' .. time .. char_id .. i

                exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), individual_item_id, 1, meta)
              end
            end

            return true, '', true
          elseif item_definition.item_expiry then
            local time = os.time()
            local char_id = characters[_source].id

            meta = {
              dur_exp_time = time + item_definition.item_expiry.dur_exp_ttl,
              dur_exp_ttl = item_definition.item_expiry.dur_exp_ttl,
            }

            if item_definition.unstackable then
              local data_key = 'core:unstackable:' .. item_id_metafree
              local counter = tonumber(DataStore.get(data_key) or 0)

              for i = 1, amount do
                counter = counter + 1

                local individual_item_id = item_id .. ':meta:' .. counter

                exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), individual_item_id, 1, meta)
              end

              DataStore.store(data_key, counter, true)
            else
              for i = 1, amount do
                local individual_item_id = item_id .. ':meta:' .. time .. char_id .. i

                exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), individual_item_id, 1, meta)
              end
            end

            return true, '', true
          elseif item_definition.unstackable then
            local data_key = 'core:unstackable:' .. item_id_metafree
            local counter = tonumber(DataStore.get(data_key) or 0)
            local individual_item_ids = {}

            for i = 1, amount do
              counter = counter + 1
              local individual_item_id = item_id .. ':meta:' .. counter

              table.insert(individual_item_ids, individual_item_id)

              exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), individual_item_id, 1, meta)
            end

            DataStore.store(data_key, counter, true)

            return true, '', true
          end
        end

        -- Special handling for gun durability / meta sequence #
        if item_definition.gun then
          local data_key = 'core:weapon_serial_sequence:' .. item_definition.gun_code
          local counter = tonumber(DataStore.get(data_key) or 0)

          for i = 1, amount do
            counter = counter + 1

            local serial = (item_definition.gun_code .. string.format('%07d', counter))

            local dur_start = nil

            if not meta then
              meta = {}
            end

            local gun_lifetime = item_definition.gun_durability

            if gun_lifetime then
              gun_lifetime = gun_lifetime.time

              if not gun_lifetime then
                gun_lifetime = GlobalState.GItemsGunDecayDuration
              else
                meta.gun_exp_life = gun_lifetime
              end

              local start_time = os.time()

              if item_is_from_storage then
                start_time = GlobalState.GItemsGunDecayInitialTime
              end

              if not meta.gun_exp_time then
                meta.gun_exp_time = start_time + gun_lifetime
              end
            end

            local individual_item_id = item_id .. ':meta:' .. serial
            local weapon_business = meta.weapon_business

            if not item_definition.illegal or weapon_business then
              meta.registration = serial

              MySQL.insert.await('INSERT INTO evidence_registrations (character_number, weapon_name, registration, business) VALUES (?, ?, ?, ?)', {
                characters[_source].id, item_definition.id, serial, weapon_business
              })
            end

            exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), individual_item_id, 1, meta)

            core.character(_source).debug('Auto register gun', individual_item_id, serial, dur_start)
          end

          DataStore.store(data_key, counter, true)

          return true, '', true
        end

        exports.blrp_inventory:DropGroundItem(_character.getCoordinates(), item_id, amount)

        return true, '', true
      end

      return _character.give(item_id, amount, meta, notify, false, callback, item_is_from_storage)
    end,

    give = function(item_id, amount, meta, notify, weight_check, callback, item_is_from_storage)
      if not weight_check then
        weight_check = false
      end

      if notify == nil then
        notify = true
      end

      if type(meta) == 'table' and next(meta) == nil then
        meta = nil
      end

      if not callback then
        callback = function() end
      end

      amount = tonumber(amount)

      if not amount then
        callback(false)
        return false
      end

      amount = math.floor(amount)

      if amount <= 0 then
        callback(false)
        return false
      end

      local item_definition, item_id_metafree = GItems.getItemDefinition(item_id)

      if not item_definition then
        callback(false)
        return false
      end

      -- Carry limit check
      local carry_limit = item_definition.carry_limit

      if weight_check and carry_limit then
        local current_quantity = core.character(_source).getItemQuantity(item_id, true)

        if current_quantity + amount > carry_limit then
          callback(false)
          return false
        end
      end

      if item_definition.client_aware then
        local clientAwareItems = core.character(_source).get('clientAwareItems') or {}
        if clientAwareItems[item_id_metafree] then
          clientAwareItems[item_id_metafree] = clientAwareItems[item_id_metafree] + amount
        else
          clientAwareItems[item_id_metafree] = amount
        end
        core.character(_source).set('clientAwareItems', clientAwareItems)
      end

      if item_id == item_id_metafree then
         -- Durability imprint for non-firearms
         if item_definition.item_durability then
          local time = os.time()
          local char_id = characters[_source].id
          local individual_item_ids = {}
          meta = {
            dur_cur = item_definition.item_durability.dur_cur or item_definition.item_durability.dur_initial,
            dur_start = item_definition.item_durability.dur_initial,
          }

          if item_definition.unstackable then
            local data_key = 'core:unstackable:' .. item_id_metafree
            local counter = tonumber(DataStore.get(data_key) or 0)

            for i = 1, amount do
              counter = counter + 1

              local individual_item_id = item_id .. ':meta:' .. counter
              table.insert(individual_item_ids,individual_item_id)
              giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta, false)
            end

            DataStore.store(data_key, counter, true)
          else
            for i = 1, amount do
              local individual_item_id = item_id .. ':meta:' .. time .. char_id .. i

              giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta, false)
            end
          end

          if notify then
            core.character(_source).notify('Received ' .. amount .. ' ' .. GItems.getItemName(item_id))
          end

          local return_value = #individual_item_ids == 1 and individual_item_ids[1] or individual_item_ids

          callback(true, return_value)
          return true, return_value
        elseif item_definition.item_expiry then
          local time = os.time()
          local char_id = characters[_source].id

          meta = {
            dur_exp_time = time + item_definition.item_expiry.dur_exp_ttl,
            dur_exp_ttl = item_definition.item_expiry.dur_exp_ttl,
          }

          if item_definition.unstackable then
            local data_key = 'core:unstackable:' .. item_id_metafree
            local counter = tonumber(DataStore.get(data_key) or 0)

            for i = 1, amount do
              counter = counter + 1

              local individual_item_id = item_id .. ':meta:' .. counter

              giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta, false)
            end

            DataStore.store(data_key, counter, true)
          else
            for i = 1, amount do
              local individual_item_id = item_id .. ':meta:' .. time .. char_id .. i

              giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta, false)
            end
          end

          callback(true)
          return true
        elseif item_definition.unstackable then
          local data_key = 'core:unstackable:' .. item_id_metafree
          local counter = tonumber(DataStore.get(data_key) or 0)
          local individual_item_ids = {}

          for i = 1, amount do
            counter = counter + 1
            local individual_item_id = item_id .. ':meta:' .. counter

            table.insert(individual_item_ids, individual_item_id)

            giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta, false)
          end

          DataStore.store(data_key, counter, true)

          if notify then
            core.character(_source).notify('Received ' .. amount .. ' ' .. GItems.getItemName(item_id, meta))
          end

          local return_value = #individual_item_ids == 1 and individual_item_ids[1] or individual_item_ids

          callback(true, return_value)
          return true, return_value
        end

        -- Auto registration for guns
        if string.match(item_id, 'wbody') then
          if item_definition.gun then
            local data_key = 'core:weapon_serial_sequence:' .. item_definition.gun_code
            local counter = tonumber(DataStore.get(data_key) or 0)
            local individual_item_ids = {}

            for i = 1, amount do
              counter = counter + 1

              local serial = (item_definition.gun_code .. string.format('%07d', counter))

              local dur_start = nil

              if not meta then
                meta = {}
              end

              local gun_lifetime = item_definition.gun_durability

              if gun_lifetime then
                gun_lifetime = gun_lifetime.time

                if not gun_lifetime then
                  gun_lifetime = GlobalState.GItemsGunDecayDuration
                else
                  meta.gun_exp_life = gun_lifetime
                end

                local start_time = os.time()

                if item_is_from_storage then
                  start_time = GlobalState.GItemsGunDecayInitialTime
                end

                if not meta.gun_exp_time then
                  meta.gun_exp_time = start_time + gun_lifetime
                end
              end

              local individual_item_id = item_id .. ':meta:' .. serial

              table.insert(individual_item_ids, individual_item_id)

              local weapon_business = meta.weapon_business

              if not item_definition.illegal or weapon_business then
                meta.registration = serial

                MySQL.insert.await('INSERT INTO evidence_registrations (character_number, weapon_name, registration, business) VALUES (?, ?, ?, ?)', {
                  characters[_source].id, item_definition.id, serial, weapon_business
                })
              end

              giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta, false)

              core.character(_source).debug('Auto register gun', item_id, serial, dur_start)
            end

            DataStore.store(data_key, counter, true)

            if notify then
              core.character(_source).notify('Received ' .. amount .. ' ' .. GItems.getItemName(item_id))
            end

            local return_value = #individual_item_ids == 1 and individual_item_ids[1] or individual_item_ids

            callback(true, return_value)
            return true, return_value
          elseif not item_definition.serial_exempt and not has_meta then
            for i = 1, amount do
              local individual_item_id = item_id .. ':meta:' .. os.date('%y%m%d') .. GetGameTimer() .. i

              giveItemToPlayerInternal(_source, individual_item_id, 1, false, meta or false, false)

              core.character(_source).debug('Auto register weapon', individual_item_id)
            end

            if notify then
              core.character(_source).notify('Received ' .. amount .. ' ' .. GItems.getItemName(item_id))
            end

            return
          end
        end

        -- Handle food items which decay
        if item_definition.category == 'food' and not food_decay_config.excluded_items[item_id_metafree] then
          local target_decay_time = food_decay_config.decay_time * 60

          if food_decay_config.decay_custom[item_id_metafree] then
            target_decay_time = food_decay_config.decay_custom[item_id_metafree]
          end

          local tt = os.date('*t')
          local decays_at = os.time{year=tt.year, month=tt.month, day=tt.day, hour=tt.hour, min=0, sec=0} + target_decay_time

          giveItemToPlayerInternal(_source, item_id_metafree .. ':meta:' .. decays_at, amount, false, meta, false)

          if notify then
            core.character(_source).notify('Received ' .. amount .. ' ' .. GItems.getItemName(item_id_metafree))
          end

          callback(true)
          return true
        end
      end

      -- Handle legacy guns to time decay system
      if item_definition.gun and (not meta or not meta.gun_exp_time) then
        if not meta then
          meta = {}
        end

        local gun_lifetime = item_definition.gun_durability

        if gun_lifetime and not meta.gun_exp_time and not meta.fpd then
          gun_lifetime = gun_lifetime.time

          local percent = 1.0
          local dur_start = meta.dur_start

          if dur_start and meta.dur_cur then
            if meta.dur_repair then
              dur_start = dur_start * (meta.dur_repair / 100)
            end

            percent = meta.dur_cur / dur_start
          end

          if not gun_lifetime then
            gun_lifetime = GlobalState.GItemsGunDecayDuration
          end

          if meta.dur_repair then
            gun_lifetime = gun_lifetime * (meta.dur_repair / 100)
          end

          if gun_lifetime ~= GlobalState.GItemsGunDecayDuration then
            meta.gun_exp_life = gun_lifetime
          end

          local start_time = os.time()

          if item_is_from_storage then
            start_time = GlobalState.GItemsGunDecayInitialTime
          end

          meta.gun_exp_time = start_time + math.floor(gun_lifetime * percent)
        end

        meta.dur_start = nil
        meta.dur_cur = nil
      end

      local success = giveItemToPlayerInternal(_source, item_id, amount, notify, meta, weight_check)

      callback(success)
      return success
    end,

    givePromise = function(item_id, amount, notify, meta, weight_check)
      return core.character(_source).give(item_id, amount, meta, notify, weight_check)
    end,

    openChest = function(chest_name, max_weight, callback, properties)
      local _character = core.character(_source)

      if _character.hasOrInheritsGroup('admin') and _character.get('ignore_inventory_weight') then
        if not properties then
          properties = {}
        end

        properties.ignore_inventory_weight = true
      end

      inventoryOpenChest(_source, chest_name, max_weight, callback, properties)
    end,

    getRandomItemFromCategory = function(category)
      local items = DataStore.get('chest:inventory:' .. characters[_source].id, false, true) or {}
      local item_ids = {}

      for item_id, _ in pairs(items) do
        if GItems.itemHasCategory(item_id, category) then
          table.insert(item_ids, item_id)
        end
      end

      if #item_ids == 0 then
        return nil
      end

      return item_ids[math.random(1, #item_ids)]
    end,

    -------------------------------
    ----------- LICENSE -----------
    -------------------------------

    hasLicense = function(license)
      local p = promise:new()

      TriggerEvent('vrp:server:getPlayerLicense', tonumber(characters[_source].identifier), license, function(status)
        p:resolve(tonumber(status))
      end)

      return tonumber(Citizen.Await(p)) == 1
    end,

    grantLicense = function(license)
      exports.vrp:GrantLicense(tonumber(characters[_source].identifier), license)
    end,

    revokeLicense = function(license)
      exports.vrp:RevokeLicense(tonumber(characters[_source].identifier), license)
    end,

    -------------------------------
    ---- BUSINESS PERM HELPERS ----
    -------------------------------

    -- Use business_name OR business_id, not both
    hasBusinessPermission = function(business_name, business_id, component_id)
      if business_name then
        return businessCanAccess(_source, business_name, component_id)
      elseif business_id then
        return checkUserCanAccess(core.character(_source), business_id, component_id)
      end
    end,
  }
end

AddEventHandler('core:server:character:registerSettings', function(player, settings)
  local character = core.character(player)
  characters[player].settings = { }

  if settings then
    for settingName, settingValue in pairs(settings) do
      characters[player].settings[settingName] = settingValue
    end

    character.sync()
  end
end)

exports('GetPlayerSetting', function(player, settingName)
  return characters[player].settings[settingName]
end)

RegisterNetEvent('core:server:character:enableItems', function(player)
  if not player then
    player = source
  end

  local character = core.character(player)

  if not character.get('items_disabled') then
    return
  end

  character.animate({ {'pickup_object', 'putdown_low', 1} })
  character.notify('Items re-enabled')
  character.set('items_disabled', false)
  character.log('ACTION', 'Re-enabled own items')
end)

core.characterFromVrp = function(vrp_id)
  vrp_id = tonumber(vrp_id)

  if not vrp_id then
    return false
  end

  for _, c_data in pairs(characters) do
    if vrp_id == tonumber(c_data.identifier) then
      return core.character(c_data.source)
    end
  end

  return false
end

core.characterFromId = function(character_id)
  character_id = tonumber(character_id)

  if not character_id then
    return false
  end

  for _, c_data in pairs(characters) do
    if character_id == tonumber(c_data.id) then
      return core.character(c_data.source)
    end
  end

  return false
end

exports('character', core.character)
exports('characterFromVrp', core.characterFromVrp)
exports('characterFromId', core.characterFromId)

exports('GenerateCharacterData', function()
  local function generatePhoneNumber()
    local result = ''

    for i = 1, 8 do
      if i ~= 4 then
        result = result .. math.random(0, 9)
      else
        result = result .. '-'
      end
    end

    if MySQL.scalar.await('SELECT phone FROM characters WHERE phone = ?', { result }) then
      return generatePhoneNumber()
    end

    return result
  end

  local function generateDlNumber()
    local result = math.random(11111111, 99999999)

    if MySQL.scalar.await('SELECT dlnumber FROM characters WHERE dlnumber = ?', { result }) then
      return generateDlNumber()
    end

    return result
  end

  local function generateDnaSequence()
    local result = createDnaSequence(math.random(1111111, 9999999))

    if MySQL.scalar.await('SELECT dna FROM characters WHERE dna = ?', { result }) then
      return generateDnaSequence()
    end

    return result
  end

  return {
    phone = generatePhoneNumber(),
    dlnumber = generateDlNumber(),
    dna = generateDnaSequence(),
  }
end)

