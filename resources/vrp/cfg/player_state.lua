
local cfg = {}

-- define the first spawn position/radius of the player (very first spawn on the server, or after death)
cfg.spawn_enabled = true -- set to false to disable the feature
cfg.respawn_positions = {
	--{307.919,-571.571,28.791}, -- Morgue
	{360.26232910156,-589.84228515625,28.656833648682}, -- Textile city
	{340.60614013672,-1396.0333251953,32.50927734375}, -- <PERSON><PERSON><PERSON>
	{-496.83184814453,-335.97592163086,34.501708984375} -- Rockford Hills
}
cfg.spawn_radius = 0.5

-- customization set when spawning for the first time
-- see https://wiki.fivem.net/wiki/Peds
-- mp_m_freemode_01 (male)
-- mp_f_freemode_01 (female)
cfg.default_customization_male = {
  model = "mp_m_freemode_01",
	[1] = {0, 0},
  [3] = {8, 0},
  [4] = {17, 0},
  [5] = {0, 0},
  [6] = {1, 0},
  [7] = {0, 0},
  [8] = {15, 0},
  [9] = {0, 0},
  [10] = {0, 0},
  [11] = {38, 0},
}

cfg.default_customization_female = {
  model = "mp_f_freemode_01",
	[1] = {0, 0},
  [3] = {2, 0},
  [4] = {4, 1},
  [5] = {0, 0},
  [6] = {3, 0},
  [7] = {0, 0},
  [8] = {14, 0},
  [9] = {0, 0},
  [10] = {0, 0},
  [11] = {40, 0},
}

cfg.default_customization_female_head = {
	["-1"] = {255,0,0},
	["0"] = {255,0,0},
	["1"] = {255,1,0},
	["2"] = {255,1,0},
	["3"] = {255,0,0},
	["4"] = {255,0,0},
	["5"] = {255,2,0},
	["6"] = {255,0,0},
	["7"] = {255,0,0},
	["8"] = {255,2,0},
	["9"] = {255,0,0},
	["10"] = {255,1,0},
	["11"] = {255,0,0},
	["12"] = {255,0,0}
}

cfg.clear_phone_directory_on_death = false
cfg.skipForceRespawn = 60*60 -- in seconds

return cfg
