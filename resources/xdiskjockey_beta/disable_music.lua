local convertedTable = {}

function CheckStatusVanillaMusic(identifier, status)
  Wait(100)
  if not identifier then return end

  local mixerData = Config.MixerList[identifier]

  if type(mixerData) ~= "table" or type(mixerData.speaker) ~= "table" then
    print(("[xdiskjockey] disable_music: missing speaker table for identifier '%s' (got %s)")
      :format(tostring(identifier), type(mixerData and mixerData.speaker)))
    return
  end

  if not mixerData.skipEmitterForThis then
    -- also guard convertedTable to avoid the same error on the next loop
    local list = (type(convertedTable) == "table") and convertedTable or {}
    for _, v in pairs(mixerData.speaker) do
      for _, val in pairs(list) do
        if #(v.pos - val.pos) < 50 then
          if status == "play" then
            SetStaticEmitterEnabled(val.name, false)
          elseif status == "stop" then
            if tableLenght(ActiveMusicCache[identifier] or {}) == 0 then
              SetStaticEmitterEnabled(val.name, true)
            end
          end
        end
      end
    end
  end
end

AddEventHandler("xdiskjockey_beta:localCheckMusicStatus", CheckStatusVanillaMusic)

RegisterNetEvent("xdiskjockey_beta:sendMusicStatus", function(status, data)
    CheckStatusVanillaMusic(data.Identifier, status)
end)

RegisterNetEvent("xdiskjockey_beta:stopAllMusic", function(mixer, queMusic)
    CheckStatusVanillaMusic(mixer, "stop")
end)

RegisterNetEvent("xdiskjockey_beta:stopAllQueMusic", function(mixer, queMusic)
    CheckStatusVanillaMusic(mixer, "stop")
end)

CreateThread(function()
    local myPos = GetEntityCoords(PlayerPedId())
    local retval = LoadResourceFile(GetCurrentResourceName(), "emitter.json")
    local data = json.decode(retval)
    for k, v in pairs(data) do
        local emmiterPos = vector3(v.Position.X, v.Position.Y, v.Position.Z)
        table.insert(convertedTable, {
            pos = emmiterPos,
            name = v.Name,
        })
    end

    Wait(1000)
    collectgarbage()
end, "Fetching all emmiter and turning it into usable table")