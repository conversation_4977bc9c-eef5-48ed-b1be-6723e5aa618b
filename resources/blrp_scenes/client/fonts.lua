---Credit https://github.com/andristum/dpscenes

Fonts = {
	{label = "Chalet Comprimé", value = "4", group = "Normal"},
	{label = "Chalet", value = "0", group = "Normal"},
	{label = "Sign Painter", value = "1", group = "Handwritten"},
	{label = "Pricedown", value = "7", group = "Misc"},
}

local AddonFonts = {
	--Normal
	{"ArialNarrow", "Arial Narrow", "Normal"},
	{"Lato", "Lato", "Normal"}, 
	-- Handwritten
	{"Inkfree", "Inkfree", "Handwritten"},
	{"<PERSON>", "Kid", "Handwritten"},
	{"Strawberry", "Strawberry", "Handwritten"},
	{"PaperDaisy", "Paper Daisy", "Handwritten"},
	{"ALittleSunshine", "A Little Sunshine", "Handwritten"},
	{"WriteMeASong", "Write Me A Song", "Handwritten"},
	-- Graffiti
	{"BeatStreet", "Beat Street", "Graffiti"},
	{"DirtyL<PERSON>rd", "Dirty Lizard", "Graffiti"},
	{"<PERSON><PERSON>", "<PERSON><PERSON>", "Graffiti"},
	-- Misc
	{"HappyDay", "Happy Day", "Misc"},
	{"ImpactLabel", "Impact Label", "Misc"},
	{"Easter", "Easter", "Misc"},
}


for i = 1, #AddonFonts do
	RegisterFontFile(AddonFonts[i][1])
	local Id = RegisterFontId(AddonFonts[i][2])
	Fonts[#Fonts+1] = {label = AddonFonts[i][2], value = tostring(Id), group = AddonFonts[i][3]}
end

BackgroundSprites = {
  { label = "None", value = "none", description = "No Background" },
  { label = "Blood", value = "Blood", description = "Bloody mess." },
  { label = "Blood 2", value = "Blood2", description = "Bloody mess." },
  { label = "Blood 3", value = "Blood3", description = "Bloody mess." },
  { label = "Blood 4", value = "Blood5", description = "Bloody mess." },
  { label = "Chain", value = "Chain", description = '"Never break the chain"' },
  { label = "Metal", value = "Metal", description = "Not to be confused with rock." },
  { label = "Note", value = "Note", description = "Colour white recommended." },
  { label = "Note 2", value = "Note2", description = "Colour white recommended." },
  { label = "Note 3", value = "Note3", description = "Colour white recommended." },
  { label = "Note 4", value = "Note4", description = "Colour white recommended." },
  { label = "Note 5", value = "Note5", description = "Colour white recommended." },
  { label = "Note 6", value = "Note6", description = "Colour white recommended." },
}
-- Function to get all labels
function getAllSpriteLabels(sprites)
  local labels = {}
  for _, sprite in ipairs(sprites) do
    table.insert(labels, sprite.label)
  end
  return labels
end

-- Function to get an entry by label
function getSpriteByLabel(sprites, searchLabel)
  for _, sprite in ipairs(sprites) do
    if sprite.label == searchLabel then
      return sprite
    end
  end
  return nil -- Return nil if no matching label is found
end