<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare_emergency</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>polbuffalop2</modelName>
      <txdName>polbuffalop2</txdName>
      <handlingId>POLBUFFALOS</handlingId>
      <gameName>polbuffalop2</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>police2</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>BUFFALO2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.055000" z="-0.035000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.005000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.020000" y="-0.055000" z="-0.035000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.005000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.040000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.570000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.455000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.015000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.015000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.298000" />
      <wheelScaleRear value="0.298000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.050000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.900000" />
      <damageOffsetScale value="0.700000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        80.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.818" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="1" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="60" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="1" />
      <flags>FLAG_LAW_ENFORCEMENT FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_HAS_LIVERY FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_GENTAXI</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_F_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
        <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
        <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_BUFFALO2_FRONT_LEFT</Item>
        <Item>STD_BUFFALO2_FRONT_RIGHT</Item>
        <Item>STD_BUFFALO_REAR_LEFT</Item>
        <Item>STD_BUFFALO_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
			<modelName>polbuffalor</modelName>
			<txdName>polbuffalor</txdName>
			<handlingId>POLBUFFALO</handlingId>
			<gameName>polbuffalor</gameName>
			<vehicleMakeName>BRAVADO</vehicleMakeName>
			<expressionDictName>null</expressionDictName>
			<expressionName>null</expressionName>
			<animConvRoofDictName>null</animConvRoofDictName>
			<animConvRoofName>null</animConvRoofName>
			<animConvRoofWindowsAffected />
			<ptfxAssetName />
			<audioNameHash>police2</audioNameHash>
			<layout>LAYOUT_STD_STRETCH</layout>
			<coverBoundOffsets>BUFFALO4_COVER_OFFSET_INFO</coverBoundOffsets>
			<explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
			<scenarioLayout />
			<cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
			<aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
			<bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
			<povCameraName>DEFAULT_POV_CAMERA</povCameraName>
			<FirstPersonDriveByIKOffset x="0.000000" y="-0.085000" z="-0.055000" />
			<FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
			<FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.120000" z="-0.080000" />
			<FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.05000" />
			<FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.070000" z="-0.080000" />
			<FirstPersonProjectileDriveByRearRightIKOffset x="-0.050000" y="-0.070000" z="-0.060000" />
			<FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.080000" z="-0.010000" />
			<FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.090000" z="-0.040000" />
			<FirstPersonDriveByRightRearPassengerIKOffset x="-0.090000" y="-0.060000" z="0.000000" />
			<FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.040000" z="0.000000" />
			<FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.000000" />
			<FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.570000" />
			<FirstPersonPassengerMobilePhoneOffset x="0.196000" y="0.203000" z="0.435000" />
			<FirstPersonMobilePhoneSeatIKOffset>
				<Item>
					<Offset x="0.136000" y="0.156000" z="0.455000" />
					<SeatIndex value="2" />
				</Item>
				<Item>
					<Offset x="0.136000" y="0.156000" z="0.455000" />
					<SeatIndex value="3" />
				</Item>
			</FirstPersonMobilePhoneSeatIKOffset>
			<PovCameraOffset x="0.000000" y="-0.145000" z="0.680000" />
			<PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
			<PovPassengerCameraOffset x="0.000000" y="-0.005000" z="-0.050000" />
			<PovRearPassengerCameraOffset x="0.018000" y="-0.045000" z="0.000000" />
			<vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
			<shouldUseCinematicViewMode value="true" />
			<shouldCameraTransitionOnClimbUpDown value="false" />
			<shouldCameraIgnoreExiting value="false" />
			<AllowPretendOccupants value="true" />
			<AllowJoyriding value="true" />
			<AllowSundayDriving value="true" />
			<AllowBodyColorMapping value="true" />
			<wheelScale value="0.249632" />
			<wheelScaleRear value="0.249632" />
			<dirtLevelMin value="0.000000" />
			<dirtLevelMax value="0.550000" />
			<envEffScaleMin value="0.000000" />
			<envEffScaleMax value="0.000000" />
			<envEffScaleMin2 value="0.000000" />
			<envEffScaleMax2 value="0.000000" />
			<damageMapScale value="0.900000" />
			<damageOffsetScale value="0.700000" />
			<diffuseTint value="0xA2000000" />
			<steerWheelMult value="1.000000" />
			<HDTextureDist value="5.000000" />
			<lodDistances content="float_array">
			  15.000000
			  30.000000
			  65.000000
			  130.000000
			  500.000000
			  500.000000
			</lodDistances>
			<minSeatHeight value="0.818" />
			<identicalModelSpawnDistance value="20" />
			<maxNumOfSameColor value="10" />
			<defaultBodyHealth value="1000.000000" />
			<pretendOccupantsScale value="1.000000" />
			<visibleSpawnDistScale value="1.000000" />
			<trackerPathWidth value="2.000000" />
			<weaponForceMult value="1.000000" />
			<frequency value="100" />
			<swankness>SWANKNESS_3</swankness>
			<maxNum value="2" />
			<flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_HAS_LIVERY</flags>
			<type>VEHICLE_TYPE_CAR</type>
			<plateType>VPT_BACK_PLATES</plateType>
			<dashboardType>VDT_RACE</dashboardType>
			<vehicleClass>VC_EMERGENCY</vehicleClass>
			<wheelType>VWT_SPORT</wheelType>
			<trailers />
			<additionalTrailers />
			<drivers>
				<Item>
					<driverName>S_F_Y_Sheriff_01</driverName>
					<npcName />
				</Item>
				<Item>
					<driverName>S_M_Y_Sheriff_01</driverName>
					<npcName />
				</Item>
			</drivers>
			<extraIncludes />
			<doorsWithCollisionWhenClosed />
			<driveableDoors />
			<bumpersNeedToCollideWithMap value="false" />
			<needsRopeTexture value="false" />
			<requiredExtras />
			<rewards></rewards>
			<cinematicPartCamera>
				<Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
				<Item>WHEEL_FRONT_LEFT_CAMERA</Item>
				<Item>WHEEL_REAR_RIGHT_CAMERA</Item>
				<Item>WHEEL_REAR_LEFT_CAMERA</Item>
			</cinematicPartCamera>
			<NmBraceOverrideSet />
			<buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
			<buoyancySphereSizeScale value="1.000000" />
			<pOverrideRagdollThreshold type="NULL" />
			<firstPersonDrivebyData>
				<Item>STANDARD_BUFFALO4_FRONT_LEFT</Item>
				<Item>STANDARD_BUFFALO4_FRONT_RIGHT</Item>
				<Item>STANDARD_BUFFALO4_REAR_LEFT</Item>
				<Item>STANDARD_BUFFALO4_REAR_RIGHT</Item>
			</firstPersonDrivebyData>
		</Item>
    <Item>
      <modelName>umkbuffalo</modelName>
      <txdName>umkbuffalo</txdName>
      <handlingId>NKSTX</handlingId>
      <gameName>POLICE4</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName />
      <audioNameHash>buffalo4</audioNameHash>
      <layout>LAYOUT_STANDARD_BUFFALO4</layout>
      <coverBoundOffsets>BUFFALO4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.085000" z="-0.055000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.120000" z="-0.080000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.05000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.070000" z="-0.080000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.050000" y="-0.070000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.080000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.090000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.090000" y="-0.060000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.040000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.570000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.196000" y="0.203000" z="0.435000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
            <Offset x="0.136000" y="0.156000" z="0.455000" />
            <SeatIndex value="2" />
        </Item>
        <Item>
            <Offset x="0.136000" y="0.156000" z="0.455000" />
            <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.005000" z="-0.050000" />
      <PovRearPassengerCameraOffset x="0.018000" y="-0.045000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.249632" />
      <wheelScaleRear value="0.249632" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.900000" />
      <damageOffsetScale value="0.700000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.818" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="60" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="1" />
       <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_Y_Cop_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
      <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
      <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
      <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
      <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
    <firstPersonDrivebyData>
        <Item>STANDARD_BUFFALO4_FRONT_LEFT</Item>
        <Item>STANDARD_BUFFALO4_FRONT_RIGHT</Item>
        <Item>STANDARD_BUFFALO4_REAR_LEFT</Item>
        <Item>STANDARD_BUFFALO4_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>emsbuffalop</modelName>
      <txdName>emsbuffalop</txdName>
      <handlingId>POLBUFFALO</handlingId>
      <gameName>emsbuffalop</gameName>
      <vehicleMakeName>BRAVADO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName />
      <audioNameHash>police3</audioNameHash>
      <layout>LAYOUT_STANDARD_BUFFALO4</layout>
      <coverBoundOffsets>BUFFALO4_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD_LONG</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.085000" z="-0.055000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.050000" y="-0.120000" z="-0.080000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.05000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.070000" z="-0.080000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="-0.050000" y="-0.070000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.070000" y="-0.080000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.090000" z="-0.040000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.090000" y="-0.060000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.040000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.020000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.135000" y="0.240000" z="0.570000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.196000" y="0.203000" z="0.435000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.136000" y="0.156000" z="0.455000" />
          <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.145000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.025000" />
      <PovPassengerCameraOffset x="0.000000" y="-0.005000" z="-0.050000" />
      <PovRearPassengerCameraOffset x="0.018000" y="-0.045000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.249632" />
      <wheelScaleRear value="0.249632" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.550000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.000000" />
      <damageMapScale value="0.900000" />
      <damageOffsetScale value="0.700000" />
      <diffuseTint value="0xA2000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        65.000000
        130.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.818" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="2" />
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_RECESSED_HEADLIGHT_CORONAS FLAG_LAW_ENFORCEMENT FLAG_EMERGENCY_SERVICE FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_REPORT_CRIME_IF_STANDING_ON FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_F_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_FIREEXTINGUISHER</Item>
        <Item>REWARD_AMMO_FIREEXTINGUISHER</Item>
        <Item>REWARD_WEAPON_FLARE</Item>
        <Item>REWARD_AMMO_FLARE</Item>
        <Item>REWARD_WEAPON_PISTOL</Item>
        <Item>REWARD_AMMO_PISTOL</Item>
        <Item>REWARD_WEAPON_STUNGUN</Item>
        <Item>REWARD_AMMO_STUNGUN</Item>
        <Item>REWARD_WEAPON_CARBINERIFLE</Item>
        <Item>REWARD_AMMO_CARBINERIFLE</Item>
        <Item>REWARD_HEALTH_ENTER_VEHICLE</Item>
        <Item>REWARD_STAT_HEALTH</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STANDARD_BUFFALO4_FRONT_LEFT</Item>
        <Item>STANDARD_BUFFALO4_FRONT_RIGHT</Item>
        <Item>STANDARD_BUFFALO4_REAR_LEFT</Item>
        <Item>STANDARD_BUFFALO4_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_poltax_interior</parent>
      <child>polbuffalop2</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>polbuffalor</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>umkbuffalo</child>
    </Item>
    <Item>
      <parent>vehicles_muscle_n_w_interior</parent>
      <child>emsbuffalop</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
