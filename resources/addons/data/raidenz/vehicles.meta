<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
 	<Item>
      <modelName>raidenz</modelName>
      <txdName>raidenz</txdName>
      <handlingId>RAIDENZ</handlingId>
      <gameName>RAIDENZ</gameName>
      <vehicleMakeName>COIL</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>omnisegt</audioNameHash>
      <layout>LAYOUT_STD_STRETCH</layout>
      <coverBoundOffsets>RAIDEN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_CHEETAH_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SPORT_LOW</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.070000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.100000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.070000" y="-0.180000" z="-0.050000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.100000" y="-0.170000" z="-0.060000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.080000" y="-0.070000" z="-0.050000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.020000" y="-0.050000" z="-0.070000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.040000" y="-0.070000" z="-0.010000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.100000" z="-0.050000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="-0.010000" y="-0.050000" z="-0.010000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.125000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.100000" z="-0.020000" />
      <FirstPersonMobilePhoneOffset x="0.120000" y="0.110000" z="0.540000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.210000" y="0.085000" z="0.416000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.180000" y="-0.020000" z="0.524000" />
          <SeatIndex value="2" />
        </Item>
        <Item>
          <Offset x="0.190000" y="-0.020000" z="0.524000" />
          <SeatIndex value="3" />
        </Item>
	    </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.270000" z="0.640000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.025000" z="0.090000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.303500" />
      <wheelScaleRear value="0.303500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.300000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.3" />
      <damageOffsetScale value="0.3" />
      <diffuseTint value="0xC4000000" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        50.000000
        100.000000
        500.000000
        500.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_DISABLE_THROUGH_WINDSCREEN FLAG_AVERAGE_CAR FLAG_IS_ELECTRIC</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	    <firstPersonDrivebyData>
        <Item>STD_RAIDEN_FRONT_LEFT</Item>
        <Item>STD_RAIDEN_FRONT_RIGHT</Item>
        <Item>STD_RAIDEN_REAR_LEFT</Item>
        <Item>STD_RAIDEN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehshare</parent>
      <child>vehicles_raiden_w_interior</child>
    </Item>
    <Item>
      <parent>vehicles_raiden_w_interior</parent>
      <child>raidenz</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>
