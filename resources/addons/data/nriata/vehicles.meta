<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>nriata</modelName>
      <txdName>nriata</txdName>
      <handlingId>NRIATA</handlingId>
      <gameName>NRIATA</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>monstrociti</audioNameHash>
      <layout>LAYOUT_4X4</layout>
      <coverBoundOffsets>MESA_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_JEEP_CAMERA</cameraName>
      <aimCameraName>JEEP_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.060000" y="-0.060000" z="-0.055000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.080000" y="-0.090000" z="-0.045000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.090000" z="-0.045000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="-0.080000" y="-0.080000" z="-0.045000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.080000" y="-0.080000" z="-0.045000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.050000" y="0.020000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.060000" y="-0.060000" z="-0.055000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="-0.050000" y="0.020000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.300000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.130000" y="0.168000" z="0.560000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.200000" y="0.173000" z="0.500000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.680000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.085000" z="-0.040000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.254400" />
      <wheelScaleRear value="0.254400" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.400000" />
      <damageOffsetScale value="0.400000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_SPOILER_MOD_DOESNT_INCREASE_GRIP</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
	  <dashboardType>VDT_RACE</dashboardType>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_SPEEDO_FRONT_LEFT</Item>
        <Item>VAN_SPEEDO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
    <Item>
      <parent>vehicles_sup1_interior</parent>
      <child>vehicles_nriata_interior</child>
    </Item>
	<Item>
      <parent>vehicles_nriata_interior</parent>
      <child>nriata</child>
    </Item>
					<Item>
      <parent>vehicles_nriata_interior</parent>
      <child>nriata2</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>