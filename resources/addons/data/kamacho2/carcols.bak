<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>
    <Sirens>
        <Item>
            <id value="190"/>
            <name>Speedo Ambulance</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="40.00000000"/>
            <lightFalloffExponent value="40.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="50.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_sirenlight</textureName>
            <sequencerBpm value="235"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="0"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="0"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="2"/>
            <rightTailLightMultiples value="2"/>
            <useRealLights value="true"/>
            <sirens>
                <Item>
                    <!-- Siren 1 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="1.57079637"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2454360650"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90"/>
                        <size value="0.75"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 2 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="1227508010"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90"/>
                        <size value="0.75"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 3 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="614081690"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90"/>
                        <size value="0.75"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 4 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="614081690"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90"/>
                        <size value="0.75"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 5 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="1227508010"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90"/>
                        <size value="0.75"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 6 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="-1.57079637"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2454360650"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="90"/>
                        <size value="0.75"/>
                        <pull value="0.2"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 7 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2861581653"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 8 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="1"/>
                        <sequencer value="0"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="3.141593"/>
                        <speed value="0"/>
                        <sequencer value="3435973836"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="10"/>
                        <size value="1.5"/>
                        <pull value="0.1"/>
                        <faceCamera value="true"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 9 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="1428726443"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 10 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="1"/>
                        <sequencer value="0"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="858993459"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="10"/>
                        <size value="1.5"/>
                        <pull value="0.1"/>
                        <faceCamera value="true"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 11 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="1430590123"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 12 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="1"/>
                        <sequencer value="0"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="3435973836"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1.5"/>
                        <pull value="0.1"/>
                        <faceCamera value="true"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 13 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2860649813"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 14 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="1"/>
                        <sequencer value="0"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="858993459"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="1.5"/>
                        <pull value="0.1"/>
                        <faceCamera value="true"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="false"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 15 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="858993471"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 16 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="3435973839"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 17 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="3435973839"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF4800"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 18 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="3"/>
                        <sequencer value="4294967295"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.141593"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="858993471"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 19 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="2.356194"/>
                        <speed value="0"/>
                        <sequencer value="4294967295"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="false"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <!-- Siren 20 -->
                    <rotation>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="0"/>
                        <multiples value="0"/>
                        <direction value="false"/>
                        <syncToBpm value="false"/>
                    </rotation>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="1431655765"/>
                        <multiples value="2"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.7"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0A00"/>
                    <intensity value="1"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
        <Item>
            <id value="60050"/>
            <name>LoreFleetLSFD</name>
            <timeMultiplier value="1.00000000"/>
            <lightFalloffMax value="100.00000000"/>
            <lightFalloffExponent value="55.00000000"/>
            <lightInnerConeAngle value="2.29061000"/>
            <lightOuterConeAngle value="70.00000000"/>
            <lightOffset value="0.00000000"/>
            <textureName>VehicleLight_misc_searchlight</textureName>
            <sequencerBpm value="550"/>
            <leftHeadLight>
                <sequencer value="0"/>
            </leftHeadLight>
            <rightHeadLight>
                <sequencer value="0"/>
            </rightHeadLight>
            <leftTailLight>
                <sequencer value="4042322160"/>
            </leftTailLight>
            <rightTailLight>
                <sequencer value="252645135"/>
            </rightTailLight>
            <leftHeadLightMultiples value="1"/>
            <rightHeadLightMultiples value="1"/>
            <leftTailLightMultiples value="1"/>
            <rightTailLightMultiples value="1"/>
            <useRealLights value="true"/>
            <sirens>
                <!-- Siren 1 - Lightbar -->
                <!-- Siren 2 - Lightbar -->
                <!-- Siren 3 - Lightbar -->
                <!-- Siren 4 - Lightbar -->
                <!-- Siren 5 - Lightbar -->
                <!-- Siren 6 - Lightbar -->
                <!-- Siren 7 - Lightbar -->
                <!-- Siren 8 - Lightbar -->
                <!-- Siren 9 -->
                <!-- Siren 10 -->
                <!-- Siren 11 - Front grill -->
                <!-- Siren 12 - Front grill -->
                <!-- Siren 13 - Front grill -->
                <!-- Siren 14 - Front grill -->
                <!-- Siren 15 - Front left pushbumper -->
                <!-- Siren 16 - Front right pushbumper -->
                <!-- Siren 17 - Rear window left -->
                <!-- Siren 18 - Rear window right -->
                <!-- Siren 19 - Rear plate left -->
                <!-- Siren 20 - Rear plate right -->
                <Item>
                    <flashiness>
                        <delta value="1.57079633"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="4"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="4"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="0"/>
                        <size value="0"/>
                        <pull value="0"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFFF"/>
                    <intensity value="0"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFFFFF"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881930"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168469130"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0"/>
                        <pull value="0.15"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="10"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.25"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694842890"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.03"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.25"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="0"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="1.57079633"/>
                        <start value="0"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694881440"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="-1.57079633"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168430090"/>
                        <multiples value="3"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.15000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="1"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="168430090"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0"/>
                        <speed value="0"/>
                        <sequencer value="2694881440"/>
                        <multiples value="2"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50"/>
                        <size value="0.8"/>
                        <pull value="0.1"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.5"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="2694842890"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <Item>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="0.00000000"/>
                        <sequencer value="168468640"/>
                        <multiples value="3"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="50.00000000"/>
                        <size value="1.10000000"/>
                        <pull value="0.20000000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFF0300"/>
                    <intensity value="0.50000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="2"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
            </sirens>
        </Item>
    </Sirens>
    <Lights/>
</CVehicleModelInfoVarGlobal>