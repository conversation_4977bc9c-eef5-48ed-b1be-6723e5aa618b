<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVariation>
    <variationData>
        <Item>
            <modelName>polthrust</modelName>
            <colors>
                <Item>
                    <indices content="char_array">
            111
            1
            0
            0
            0
            0
          </indices>
                    <liveries>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                    </liveries>
                </Item>
            </colors>
            <kits/>
            <windowsWithExposedEdges/>
            <plateProbabilities>
                <Probabilities>
                    <Item>
                        <Name>Police guv plate</Name>
                        <Value value="100"/>
                    </Item>
                </Probabilities>
            </plateProbabilities>
            <lightSettings value="12"/>
            <sirenSettings value="56"/>
        </Item>
        <Item>
            <modelName>policeb5</modelName>
            <colors>
                <Item>
                    <indices content="char_array">
            0
            0
            0
            0
            0
            0
          </indices>
                    <liveries>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                    </liveries>
                </Item>
            </colors>
            <kits/>
            <windowsWithExposedEdges/>
            <plateProbabilities>
                <Probabilities>
                    <Item>
                        <Name>Police guv plate</Name>
                        <Value value="100"/>
                    </Item>
                </Probabilities>
            </plateProbabilities>
            <lightSettings value="1"/>
            <sirenSettings value="57"/>
        </Item>
        <Item>
            <modelName>policeb4</modelName>
            <colors>
                <Item>
                    <indices content="char_array">
            0
            0
            0
            0
            0
            0
          </indices>
                    <liveries>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                        <Item value="false"/>
                    </liveries>
                </Item>
            </colors>
            <kits/>
            <windowsWithExposedEdges/>
            <plateProbabilities>
                <Probabilities>
                    <Item>
                        <Name>Police guv plate</Name>
                        <Value value="100"/>
                    </Item>
                </Probabilities>
            </plateProbabilities>
            <lightSettings value="1"/>
            <sirenSettings value="57"/>
        </Item>
    </variationData>
</CVehicleModelInfoVariation>