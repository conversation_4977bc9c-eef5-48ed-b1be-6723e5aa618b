@font-face {
    font-family: Box;
    src: url(./ios.ttf);
}

body {
    user-select: none;
    overflow: hidden;
    font-family: Box;
    font-weight: 550;
}

.container {
    position: absolute;
    background: rgba(23, 25, 51, 0.8);
    box-shadow: 0 0 30px inset rgba(12, 12, 19, 0.8), 0 0 4px black; 
    border: 1px solid #252f4c solid;
    border-radius: .3vw;
    width: 28.9vw;
    height: 30vw;
    left: 84vw;
    top: 66%;
    transform: translate(-50%, -50%);
    padding: .5vw;
    display: flex;
    flex-direction: column;
    gap: .5vw;
}

.searchbar{
    background: rgba(36, 38, 60, 0.8) url("search.svg") no-repeat 15px center;
    width: 26.6vw;
    height: 2.5vw;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: white;
    border: none;
    box-shadow: 0 0 30px inset rgba(12, 12, 19, 0.8), 0 0 4px black; 
    background-size: 15px 15px;
    padding-left: 1.9vw;
    padding-top: .2vw;
    outline: none;
}

.categories {
    /* background-color: rgb(126, 126, 126); */
    width: 100%;
    height: 3.5vw;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: .5vw;
}

.cat {
    background-color: rgba(36, 38, 60, 0.212);
    box-shadow: 0 0 30px inset rgba(12, 12, 19, 0.8), 0 0 4px black; 
    border: 2px solid #121729 solid;
    margin-top: 15px;
    width: 6vw;
    height: 3.2vw;
    border-radius: .4vw;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: relative;
}

.cat:hover::after {
    /* Set styles for the tooltip */
    content: attr(title);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 10px;
    background-color: #000;
    color: #fff;
    font-size: 14px;
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.cat:hover::after {
    /* Show the tooltip on hover */
    opacity: 1;
    visibility: visible;
}

.cat:hover{
    cursor: pointer;
}

.emotes {
    height: 100%;
    border-radius: .3vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-content: flex-start;
    gap: 1.5%;
    flex: 1 1 auto;
    overflow: auto; /* Use auto to show scrollbar only when necessary */
  }
  
  .emote {
    background-color: rgba(36, 38, 60, 0.212);
    box-shadow: 0 0 30px inset rgba(12, 12, 19, 0.8), 0 0 4px black; 
    border-radius: 0.3vw;
    width: 8.99vw;
    height: 3.5vw;
    overflow: hidden;
    text-overflow: ellipsis; /* Add this property to display ellipsis (...) for overflowed text */
    white-space: nowrap; /* Add this property to prevent line breaks in the text */
  }
  
  .emotes .emote:nth-child(-n+3) {
    margin-top: 15px; /* Adjust the margin-top value as needed */
  }
  

.emote:hover {
    cursor: pointer;
    outline: 1px solid deepskyblue;
    box-shadow: 0px 0px 7px deepskyblue;
}
.emote-title {
    font-size: .8vw;
    margin: .4vw;
    color: aliceblue;
    font-weight: bold;
}
.emote-command {
    font-size: .8vw;
    margin: .4vw;
    color: aliceblue;
    margin-top: .2vw;
    font-weight: 100;
}
.search-icon {
    margin-left: .3vw;
    font-size: 1vw;
    color: rgb(218, 218, 218);
}
.search-icon:hover {
    color: rgb(230, 230, 230);
    cursor: pointer;
}

.icono-cat {
    font-size: 1.5vw;
    color: white;
}

.cat:hover .icono-cat {
    color: deepskyblue;
}

::-webkit-scrollbar{
    position:absolute;
    width:.3vw;
    height:.10vw;
    left:-.5vw;
    transition: .5s;
}
::-webkit-scrollbar-button{
    width:0;
    height:0;
}
::-webkit-scrollbar-thumb{
    background: white;
    border-radius:20vw;
}

::-webkit-scrollbar-thumb:hover{
    background:deepskyblue;
    cursor: pointer;
} 
::-webkit-scrollbar-corner{
    background: none;
}