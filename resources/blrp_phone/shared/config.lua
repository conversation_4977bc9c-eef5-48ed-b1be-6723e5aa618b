c_phone = {
    heartbeat_wait_time = 1000, -- in ms
    max_call_time = 60 * 60 * 1000, -- in ms
    no_answer_call_timeout = 30 * 1000, -- in ms


    world_phones = {
        -- VWPD
        ['911'] = {
            name = 'Vinewood PD Reception',
            coords = { x = 617.5511, y = 3.398285, z = 87.65079 },
            ringtone_enabled = true,
        },

        ['912'] = {
            name = 'Sandy Shores SO',
            coords = { x = 1830.472, y = 3683.695, z = 34.326 },
            ringtone_enabled = true,
        },

        ['913'] = {
          name = 'Vespucci PD Reception',
          coords = { x = -1095.647, y = -837.0481, z = 19.31911 },
          ringtone_enabled = true,
        },

        -- <PERSON><PERSON> reception
        ['911-4377'] = {
            name = 'Emergency Line',
            coords = { x = 879.2722, y = -2267.904, z = 32.66172 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- <PERSON><PERSON> rear
        ['911-3626'] = {
            name = 'Emergency Line',
            coords = { x = 871.9271, y = -2293.841, z = 32.6678 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Central Bank upper
        ['911-8994'] = {
            name = 'Emergency Line',
            coords = { x = 253.1454, y = 222.1222, z = 106.3827 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Central Bank security
        ['911-5192'] = {
            name = 'Emergency Line',
            coords = { x = 264.9589, y = 220.6239, z = 101.6614 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Blaine County Savings Bank
        ['911-2157'] = {
            name = 'Emergency Line',
            coords = { x = -113.0956, y = 6472.596, z = 31.73782 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca East Joshua
        ['911-9932'] = {
            name = 'Emergency Line',
            coords = { x = 2450.385, y = 4065.2, z = 38.39788 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca Route 68
        ['911-6408'] = {
            name = 'Emergency Line',
            coords = { x = 1178.852, y = 2710.489, z = 38.31864 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca Great Ocean Hwy
        ['911-9289'] = {
            name = 'Emergency Line',
            coords = { x = -2959.049, y = 479.0108, z = 15.974 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca Rockford Hills
        ['911-7618'] = {
            name = 'Emergency Line',
            coords = { x = -1214.37, y = -335.7166, z = 38.03564 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca Burton
        ['911-4370'] = {
            name = 'Emergency Line',
            coords = { x = -355.7397, y = -52.13568, z = 49.28957 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca Alta
        ['911-4040'] = {
            name = 'Emergency Line',
            coords = { x = 309.41, y = -281.2524, z = 54.42512 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- Fleeca Legion Square
        ['911-1602'] = {
            name = 'Emergency Line',
            coords = { x = 145.0689, y = -1042.881, z = 29.62988 },
            block_outbound = true,
            ringtone_enabled = true,
        },

        -- BAD-RADI
        ['223-7234'] = { 
            name = 'BadRadio (223-7234)',
            coords = { x = -1014.686, y = -274.0732, z = 44.87603 },
            block_outbound = false,
            conference_enabled = true,
        },

        -- Prison Phone 555-JAIL
        ['555-5245'] = { 
            name = 'Prison Phone (555-5245)',
            coords = { x = 1828.766, y = 2579.863, z = 46.52822 },
            block_outbound = false,
            ringtone_enabled = true,
        },
    },

    services = {
        {
            display = 'Police 👮',
            color = '#4d91ff',
            group = 'Police',
            items = {
                {
                    title = 'Send Emergency Message',
                    event = 'triggerServiceText',
                    type = 'message_police_emergency'
                },
                {
                    title = 'Send Emergency (Anon)',
                    event = 'triggerServiceText',
                    type = 'message_police_emergency_anonymous'
                },
                {
                    title = 'Send Non-Emergency Message',
                    event = 'triggerServiceText',
                    type = 'message_police'
                },
                {
                    title = 'Send Non-Emergency (Anon)',
                    event = 'triggerServiceText',
                    type = 'message_police_anonymous'
                },
                {
                    title = 'Call Vinewood PD',
                    event = 'triggerServiceCall',
                    type = '911'
                },
                {
                    title = 'Call Sandy Shores SO',
                    event = 'triggerServiceCall',
                    type = '912'
                },
                {
                  title = 'Call Vespucci PD',
                  event = 'triggerServiceCall',
                  type = '913'
              },
            },
        },
        {
            display = 'EMS 🏥',
            color = '#ff4824',
            group = 'LSFD',
            items = {
                {
                    title = 'Send a Message',
                    event = 'triggerServiceText',
                    type = 'message_emergency'
                },
            }
        },
        {
            display = 'Taxi 🚕',
            color = '#ffb224',
            group = 'Taxi',
            items = {
                {
                    title = 'Send a Message',
                    event = 'triggerServiceText',
                    type = 'message_taxi'
                },
                {
                    title = 'Send a Message (Anon)',
                    event = 'triggerServiceText',
                    type = 'message_taxi_anonymous'
                },
            }
        },
        {
            display = 'Tow Truck ⛓️',
            color = '#ff991c',
            group = 'Tow Truck',
            items = {
                {
                    title = 'Send a Message',
                    event = 'triggerServiceText',
                    type = 'message_towtruck'
                },
            }
        },
        {
            display = 'Legal Services ✒️',
            color = '#e263ff',
            group = 'Lawyer',
            items = {
                {
                    title = 'Send a Message',
                    event = 'triggerServiceText',
                    type = 'message_lawyer'
                },
            }
        },
        {
            display = 'Department of Justice ⚖️',
            color = '#97ff63',
            group = 'DOJ',
            items = {
                {
                    title = 'Send a Message',
                    event = 'triggerServiceText',
                    type = 'message_doj'
                },
            }
        },
        {
            display = 'Department of Corrections',
            color = '#00b539',
            group = 'DOC',
            items = {
                {
                    title = 'Send a Message',
                    event = 'triggerServiceText',
                    type = 'message_doc'
                },
            }
        },
        {
          display = 'Weazel News 📰',
          color = '#9797a5',
          group = 'Weazel News',
          items = {
            {
                title = 'Send a Message',
                event = 'triggerServiceText',
                type = 'message_weazel'
            },
            {
              title = 'Send a Message (Anon)',
              event = 'triggerServiceText',
              type = 'message_weazelanon'
            },
          }
        },
    }
}
