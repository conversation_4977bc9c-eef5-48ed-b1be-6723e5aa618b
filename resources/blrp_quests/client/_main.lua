tQuests = {}
T.bindInstance('main', tQuests)

peds = {}

exports('RegisterPed', function(id, handle)
  peds[id] = handle
end)

exports('GetPedHandle', function(id)
  return peds[id]
end)

local running_dialogue = nil
local running_dialogue_index = 0
local running_dialogue_promise = nil
local running_dialogue_mugshots = {}

function getMugshotForPed(ped_id)
  local ped = peds[ped_id]

  if ped_id == 'SELF' then
    ped = PlayerPedId()
  end

  if not ped then
    return nil
  end

  local handle = RegisterPedheadshot(ped)

  local timer = 2000

  local start_time = GetGameTimer()

  while not handle or not IsPedheadshotReady(handle) or not IsPedheadshotValid(handle) do
    Citizen.Wait(10)

    if GetGameTimer() - start_time > 2000 then
      break
    end
  end

  if not handle or not IsPedheadshotReady(handle) or not IsPedheadshotValid(handle) then
    return nil
  end

	local txd = GetPedheadshotTxdString(handle)

  return txd
end

tQuests.runXmasDialogue = function(dialogue)
  return tQuests.runDialogue(dialogue, 'xmas')
end

tQuests.runBlrpDialogue = function(dialogue)
  return tQuests.runDialogue(dialogue, 'blrp')
end

function unpackStatic(static)
  local pos = string.find(static, '/')
  local dict = string.sub(static, 8, pos - 1)
  local img = string.sub(static, pos + 1, string.len(static))

  return dict, img
end

tQuests.runDialogue = function(dialogue, theme)
  if not theme then
    theme = 'default'
  end

  running_dialogue = dialogue
  running_dialogue_index = 1
  running_dialogue_mugshots = {}
  running_dialogue_dicts = {}

  for i = 1, 30 do
    UnregisterPedheadshot(i)
  end

  for _, line in pairs(dialogue) do
    local ped_left, ped_right, text = table.unpack(line)

    for _, v in pairs({ ped_left, ped_right }) do
      if v then
        if string.match(v, 'static') then
          local dict, img = unpackStatic(v)

          running_dialogue_dicts[dict] = true

          while not HasStreamedTextureDictLoaded(dict) do
            RequestStreamedTextureDict(dict)
            Wait(0)
          end
        elseif not running_dialogue_mugshots[v] then
          running_dialogue_mugshots[v] = getMugshotForPed(v)
        end
      end
    end
  end

  if running_dialogue_promise then
    running_dialogue_promise:resolve(false)
  end

  running_dialogue_promise = promise:new()

  Citizen.Wait(100)

  SendNUIMessage({
    action = 'setQuestInfo',
    img_dicts = running_dialogue_mugshots,
    quest_text = running_dialogue[running_dialogue_index],
    theme = theme,
  })

  SetNuiFocus(true, false)

  local response = Citizen.Await(running_dialogue_promise)

  for dict, _ in pairs(running_dialogue_dicts) do
    SetStreamedTextureDictAsNoLongerNeeded(dict)
  end

  return response
end

exports('RunDialogue', tQuests.runDialogue)

RegisterNUICallback('keyPressed', function(data, callback)
  if not running_dialogue or not running_dialogue_index or not running_dialogue[running_dialogue_index] then
    return
  end

  local running_options = running_dialogue[running_dialogue_index][3]

  callback('ok')

  if
    type(running_options) == 'table' and
    (
      type(data.code) ~= 'number' or
      data.code < 1 or
      data.code > #running_options
    )
  then
    return
  end

  if type(running_options) == 'string' and data.code ~= 'Space' then
    return
  end

  local resolution_value = true

  if type(running_options) == 'table' then
    resolution_value = data.code
  end

  running_dialogue_index = running_dialogue_index + 1

  local next_dialogue = running_dialogue[running_dialogue_index]

  if not next_dialogue and running_dialogue_promise then
    running_dialogue_promise:resolve(resolution_value)
    SendNUIMessage({ action = 'hide' })
    SetNuiFocus(false, false)
    running_dialogue_promise = nil
    return
  end

  if not next_dialogue then
    return
  end

  SendNUIMessage({
    action = 'setQuestInfo',
    img_dicts = running_dialogue_mugshots,
    quest_text = next_dialogue
  })
end)

RegisterNUICallback('escapePressed', function(_, callback)
  callback('ok')

  if running_dialogue_promise then
    running_dialogue_promise:resolve(false)
    running_dialogue_promise = nil
  end

  SendNUIMessage({ action = 'hide' })
  SetNuiFocus(false, false)
end)

AddEventHandler('menu:forceCloseMenu', function()
  if running_dialogue_promise then
    running_dialogue_promise:resolve(false)
    running_dialogue_promise = nil
  end

  SendNUIMessage({ action = 'hide' })
  SetNuiFocus(false, false)
end)

-- Image (sprite) presentation
local drawing_sprite = false

-- Width and height range 0.0 - 1.0, should match the aspect ratio of the texture being used
tQuests.drawSprite = function(texture_dict, texture_name, screen_x, screen_y, width, height, heading, r, g, b, a)
  local start = GetGameTimer()

  while not HasStreamedTextureDictLoaded(texture_dict) do
    Wait(0)
    RequestStreamedTextureDict(texture_dict)

    if GetGameTimer() - start > 10000 then
      return false
    end
  end

  -- Kill existing threads
  if drawing_sprite then
    drawing_sprite = false
    exports.mythic_notify:PersistentAlert('END', 'QuestSprite')
  end

  Citizen.CreateThread(function()
    drawing_sprite = true
    exports.mythic_notify:PersistentAlert('START', 'QuestSprite', 'inform', 'Press E to close')

    while drawing_sprite do
      DrawSprite(texture_dict, texture_name, screen_x, screen_y, width, height, heading, r, g, b, a)
      Wait(0)

      if IsControlJustReleased(0, 51) then
        drawing_sprite = false
        exports.mythic_notify:PersistentAlert('END', 'QuestSprite')
      end
    end
  end)
end

tQuests.removeSprite = function()
  drawing_sprite = false
  exports.mythic_notify:PersistentAlert('END', 'QuestSprite')
end
