local transaction_queue = {}

--[[
  transaction {
    amount = (int), -- Amount
    transactor = (string), -- Name of transactor (person, business, etc)
    note = (string), -- Note
    hidden = [bool], -- Hidden
  }
]]
exports('ApplyTransactionTax', function(transaction)
  if transaction.hidden == nil then
    transaction.hidden = true
  end

  transaction.timestamp = os.date("%Y-%m-%d %H:%M:%S")

  table.insert(transaction_queue, transaction)
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    for k, transaction in pairs(transaction_queue) do
      local rows_affected = MySQL.Sync.execute('UPDATE bank_accounts SET balance = balance + @amount WHERE account_number = 1000010', {
        amount = transaction.amount
      })

      if rows_affected <= 0 then
        return
      end

      MySQL.Async.execute('INSERT INTO bank_account_transactions (account_number, transaction_uuid, transactor_name, transaction_type, note, amount, hidden, timestamp) VALUES (1000010, @transaction_uuid, @transactor_name, @transaction_type, @note, @amount, @hidden, @timestamp)', {
        transaction_uuid = (transaction.uuid or randomUuid()),
        transactor_name = transaction.transactor,
        transaction_type = 'TAX DEPOSIT',
        note = transaction.note,
        amount = transaction.amount,
        hidden = transaction.hidden,
        timestamp = transaction.timestamp,
      })

      table.remove(transaction_queue, k)
    end
  end
end)
