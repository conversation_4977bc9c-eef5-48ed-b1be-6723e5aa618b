POS_actual = 1
PED_hasBeenTeleported = false
local me = exports.blrp_core.me()

function DrawText3Ds(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z)
  local pX, pY, pZ = table.unpack(GetGameplayCamCoords())
  local dist = GetDistanceBetweenCoords(pX, pY, pZ, x, y, z, 1)

  local scale = 0.4
  --local fov = (1/GetGameplayCamFov())*100
  --local scale = scale*fov

  if onScreen then
    SetTextScale(scale, scale)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextEntry("STRING")
    SetTextCentre(1)
    SetTextColour(255, 255, 255, 215)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0150, 0.030 + factor, 0.025, 41, 11, 41, 100)
  end
end

function teleport(pos)
    TriggerEvent('vrp:setCheckDelayed',30)
    Citizen.Wait(50)
    local ped = PlayerPedId()
    Citizen.CreateThread(function()
        PED_hasBeenTeleported = true
      -- applies a quick teleport & fadein/out
        DoScreenFadeOut(1000)
        while IsScreenFadingOut() do Citizen.Wait(0) end
        RequestCollisionAtCoord(pos.x, pos.y, pos.z)
        SetEntityCoords(ped, pos.x, pos.y, pos.z, true, true, true, false)
        SetEntityHeading(ped, pos.h)
        DoScreenFadeIn(1000)
        while IsScreenFadingIn() do Citizen.Wait(0)	end

        Citizen.Wait(500)
        PED_hasBeenTeleported = false
        
    end)
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        local ped = PlayerPedId()
        local playerPos = GetEntityCoords(ped, true)

        for i,pos in pairs(Teleports) do
            DrawMarker(42, pos.x, pos.y, pos.z-0.99, 0.0, 0.0, 0.0, 270.0, 1.0, 0, 0.75, 0.5, 0.75, 0, 0, 0, 96, false, 0, 2, true, 0, 0, 0)
            -- Used for Making and Testing other Markers for other Resources
            --DrawMarker(28, pos.x, pos.y, pos.z-0.99, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 10.0, 10.0, 5.0, 255, 0, 0, 128, false, 0, 2, false, 0, 0, 0)

            if (Vdist(playerPos.x, playerPos.y, playerPos.z, pos.x, pos.y, pos.z) < 0.5) and (not PED_hasBeenTeleported) then
                POS_actual = pos.id
                if not gui_interiors.opened then
                    gui_interiors_OpenMenu()
                end
            end
        end
    end
end)
