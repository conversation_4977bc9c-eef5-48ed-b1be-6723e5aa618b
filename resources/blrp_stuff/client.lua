-- Crosshair state management
local lastcarView = 0
local xhairActive = false
local current_weapon = false
local lastShooting = 0

-- State tracking for race condition prevention
local previousAiming = false
local previousShooting = false
local previousWeapon = false
local previousInVehicle = false
local previousCameraMode = 0

-- Timing and debouncing
local crosshairShowTimer = 0
local crosshairHideTimer = 0
local stateChangeTimer = 0
local lastStateChange = 0

-- Configuration
local CROSSHAIR_SHOW_DELAY = 300  -- ms delay before showing crosshair
local CROSSHAIR_HIDE_DELAY = 600  -- ms delay before hiding crosshair
local STATE_DEBOUNCE_TIME = 100   -- ms debounce for state changes
local UPDATE_FREQUENCY = 50       -- ms between updates (20 FPS)

local noCrossHair = {
  --[911657153] = true, -- weapon_stungun
  [-1569615261] = true, --weapon_unarmed
  [`WEAPON_FLASHLIGHT`] = true,
  [`WEAPON_FLASHLIGHT_UV`] = true,
  [101631238] = true, --weapon_fireextinguisher
  --[683870287] = true, --WEAPON_PLASMAP
  [`WEAPON_FLAREGUN`] = true,
  [`WEAPON_GRENADELAUNCHER_SMOKE`] = true,
}

local crosshairVehicles = {
  [`hydra`] = true,
  [`lazer`] = true,
  [`starling`] = true,
  [`rogue`] = true,
  [`valkyrie`] = true,
  [`valkyrie2`] = true,
  [`savage`] = true,
  [`hunter`] = true,
  [`conada2`] = true,
  [`buzzard`] = true,
  [`annihilator`] = true,
  [`rhino`] = true,
  [`akula`] = true,
  [`khanjali`] = true,
  [`barrage`] = true,
  [`apc`] = true,
  [`nightshark`] = true,
  [`insurgent3`] = true,
  [`insurgent`] = true,
  [`technical3`] = true,
  [`technical`] = true,
}

-- Helper functions for state management
local function isPlayerInAnimation()
  local ped = PlayerPedId()
  return IsEntityPlayingAnim(ped, "", "", 3) or IsPedInAnyVehicle(ped, false) and IsEntityPlayingAnim(ped, "", "", 1)
end

local function isValidCrosshairState(isAiming, isShooting, weapon, inVehicle, cameraMode)
  -- Don't show crosshair for blacklisted weapons
  if noCrossHair[weapon] then
    return false
  end

  -- Must be aiming or shooting
  if not isAiming and not isShooting then
    return false
  end

  -- Vehicle-specific checks
  if inVehicle then
    -- Must be in first-person view (mode 4) or helicopter
    return cameraMode == 4 or IsPedInAnyHeli(PlayerPedId())
  end

  return true
end

local function shouldShowCrosshair(isAiming, isShooting, weapon, inVehicle, cameraMode)
  return isValidCrosshairState(isAiming, isShooting, weapon, inVehicle, cameraMode)
end

local function resetCrosshairState()
  if xhairActive then
    SendNUIMessage({type = "xhairHide"})
    xhairActive = false
  end
  current_weapon = false
  crosshairShowTimer = 0
  crosshairHideTimer = 0
end

-- Race-condition-free crosshair state update function
function updateCrosshairState(isAiming, isShooting, weapon, inVehicle, cameraMode, currentTime)
  -- Detect state changes
  local stateChanged = (isAiming ~= previousAiming) or
                      (isShooting ~= previousShooting) or
                      (weapon ~= previousWeapon) or
                      (inVehicle ~= previousInVehicle) or
                      (cameraMode ~= previousCameraMode)

  -- Debounce rapid state changes
  if stateChanged then
    if currentTime - lastStateChange < STATE_DEBOUNCE_TIME then
      return -- Too soon, ignore this change
    end
    lastStateChange = currentTime
    stateChangeTimer = currentTime
  end

  -- Update previous state
  previousAiming = isAiming
  previousShooting = isShooting
  previousWeapon = weapon
  previousInVehicle = inVehicle
  previousCameraMode = cameraMode

  -- Check if player is in an animation that might interfere
  if isPlayerInAnimation() and not inVehicle then
    -- Reset crosshair during animations to prevent glitches
    if xhairActive then
      resetCrosshairState()
    end
    return
  end

  -- Determine if crosshair should be shown
  local shouldShow = shouldShowCrosshair(isAiming, isShooting, weapon, inVehicle, cameraMode)

  -- Handle crosshair visibility with proper timing
  if shouldShow and not xhairActive then
    -- Start show timer if not already started
    if crosshairShowTimer == 0 then
      crosshairShowTimer = currentTime
      crosshairHideTimer = 0 -- Cancel any hide timer
    elseif currentTime - crosshairShowTimer >= CROSSHAIR_SHOW_DELAY then
      -- Show delay has passed, validate state is still correct
      if shouldShowCrosshair(isAiming, isShooting, weapon, inVehicle, cameraMode) then
        SendNUIMessage({type = "xhairShow"})
        xhairActive = true
        current_weapon = weapon
        crosshairShowTimer = 0
      end
    end
  elseif not shouldShow and xhairActive then
    -- Start hide timer if not already started
    if crosshairHideTimer == 0 then
      crosshairHideTimer = currentTime
      crosshairShowTimer = 0 -- Cancel any show timer
    elseif currentTime - crosshairHideTimer >= CROSSHAIR_HIDE_DELAY then
      -- Hide delay has passed, validate state is still correct
      if not shouldShowCrosshair(isAiming, isShooting, weapon, inVehicle, cameraMode) then
        SendNUIMessage({type = "xhairHide"})
        xhairActive = false
        current_weapon = false
        crosshairHideTimer = 0
      end
    end
  elseif not shouldShow and not xhairActive then
    -- Reset timers when not showing and shouldn't show
    crosshairShowTimer = 0
    crosshairHideTimer = 0
    current_weapon = false
  elseif shouldShow and xhairActive then
    -- Reset timers when showing and should show
    crosshairShowTimer = 0
    crosshairHideTimer = 0
    current_weapon = weapon
  end
end

local crosshairConfig = {
  color = "white",
  size = 2
}

function saveCrosshairConfig()
  SetResourceKvp("crosshairConfig", json.encode(crosshairConfig))
end

function loadCrosshairConfig()
  local savedConfig = GetResourceKvpString("crosshairConfig")
  if savedConfig then
    crosshairConfig = json.decode(savedConfig)
  else
    crosshairConfig = { color = "white", size = 2 }
  end
  updateCrosshairConfig()
end

function updateCrosshairConfig()
  SendNUIMessage({
    type = "updateConfig",
    color = crosshairConfig.color,
    size = crosshairConfig.size
  })
end

-- Add commands to change crosshair color and size
RegisterCommand("crosshaircolor", function(source, args, rawCommand)
  local color = args[1]
  if color then
    crosshairConfig.color = color
    saveCrosshairConfig()
    updateCrosshairConfig()
  end
end, false)

RegisterCommand("crosshairsize", function(source, args, rawCommand)
  local size = tonumber(args[1])
  if size then
    crosshairConfig.size = size
    saveCrosshairConfig()
    updateCrosshairConfig()
  end
end, false)

-- Load the crosshair config when the script starts
Citizen.CreateThread(function()
  loadCrosshairConfig()
end)

local isStaff = false
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(10000)
    isStaff = exports.blrp_core:me().isStaff()
  end
end)

-- Handle weapon changes and ensure proper cleanup
Citizen.CreateThread(function()
  local lastWeapon = GetHashKey("WEAPON_UNARMED")

  while true do
    Citizen.Wait(500) -- Check every 500ms

    local playerPed = PlayerPedId()
    local _, currentWeapon = GetCurrentPedWeapon(playerPed, true)

    -- If weapon changed, reset crosshair state to prevent glitches
    if currentWeapon ~= lastWeapon then
      resetCrosshairState()
      lastWeapon = currentWeapon
    end
  end
end)

-- Handle animation interruptions and task changes
AddEventHandler('onClientResourceStart', function(resourceName)
  if resourceName == GetCurrentResourceName() then
    -- Reset state on resource start
    resetCrosshairState()
  end
end)

-- Export function for other resources to reset crosshair state
exports('ResetCrosshair', resetCrosshairState)

-- HUD management thread (must run every frame for HUD hiding)
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0) -- Must be 0 for HUD components to work properly
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    local vehicleModel = vehicle ~= 0 and GetEntityModel(vehicle) or false

    -- Hide various HUD components
    HideHudComponentThisFrame(1)
    -- HideHudComponentThisFrame(2) -- Weapon icon
    HideHudComponentThisFrame(3)
    HideHudComponentThisFrame(4)
    HideHudComponentThisFrame(7)
    HideHudComponentThisFrame(9)
    HideHudComponentThisFrame(13)

    -- Hide crosshair component unless we have hunting rifle and crosshair is active
    if not xhairActive or (xhairActive and current_weapon ~= `WEAPON_HUNTINGRIFLE`) then
      HideHudComponentThisFrame(14)
    end

    if isStaff and vehicleModel and crosshairVehicles[vehicleModel] then
      SendNUIMessage({type = "xhairHide"})
      xhairActive = false
      ShowHudComponentThisFrame(14)
    end

    -- Track shooting for crosshair timing
    if IsPedShooting(playerPed) then
      lastShooting = GetGameTimer()
    end
  end
end)

-- Main crosshair and camera management thread
Citizen.CreateThread(function()
  local isForcedFirstPerson = false -- Track if we forced first-person view
  local lastViewRestoreTime = 0 -- Prevent rapid toggling
  local debounceTime = 500 -- Debounce time in milliseconds

  while true do
    Citizen.Wait(UPDATE_FREQUENCY) -- Reduced frequency for better performance

    -- Checks if a specific control is pressed and the player is in a vehicle
    if (IsControlPressed(0, 85) or IsDisabledControlPressed(0, 85) or IsHudComponentActive(16)) and IsPedInAnyVehicle(PlayerPedId(), false) then
      DisableControlAction(0, 25) -- Disables AIM (Right Mouse Button)
      DisableControlAction(0, 50) -- Disables ARW_ATTACK (Mouse Wheel Up)
      DisableControlAction(0, 68) -- Disables ARW_SELECT (G)
      DisableControlAction(0, 91) -- Disables SELECT_RADIO (F1)

      -- Reset crosshair when controls are disabled
      resetCrosshairState()
    end

    local playerPed = PlayerPedId()
    local isAiming = IsPlayerFreeAiming(PlayerId())
    local isShooting = (GetGameTimer() - lastShooting < 2000)
    local _, cur_weap = GetCurrentPedWeapon(playerPed, true)
    local currentTime = GetGameTimer()
    local inVehicle = IsPedInAnyVehicle(playerPed, false)
    local cameraMode = inVehicle and GetFollowVehicleCamViewMode() or 0

    -- Handle vehicle camera view and drive-by
    if inVehicle and (isAiming or isShooting) then
      if cameraMode ~= 4 then
        lastcarView = cameraMode
        SetFollowVehicleCamViewMode(4)
        isForcedFirstPerson = true
        lastViewRestoreTime = currentTime
        SetPlayerCanDoDriveBy(PlayerId(), true) -- Enable drive-by in first-person
        cameraMode = 4 -- Update local variable
      end
    elseif isForcedFirstPerson and not isAiming and not isShooting and (currentTime - lastViewRestoreTime > debounceTime) then
      -- Restore previous view mode and disable drive-by if not in first-person
      if lastcarView ~= nil and lastcarView ~= 4 then
        SetFollowVehicleCamViewMode(lastcarView)
        isForcedFirstPerson = false
        SetPlayerCanDoDriveBy(PlayerId(), false) -- Disable drive-by when not in first-person
        cameraMode = lastcarView -- Update local variable
      end
    elseif not inVehicle or cameraMode ~= 4 then
      -- Ensure drive-by is disabled if not in vehicle or not in first-person
      SetPlayerCanDoDriveBy(PlayerId(), false)
    end

    -- Update crosshair state using new race-condition-free logic
    updateCrosshairState(isAiming, isShooting, cur_weap, inVehicle, cameraMode, currentTime)
  end
end)

-- Citizen.CreateThread(function()
--   while true do
--     Citizen.Wait(1000)
--
--     for _, player_id in pairs(GetActivePlayers()) do
--       local player_ped = GetPlayerPed(player_id)
--
--       local aim_test = false
--
--       if player_id == PlayerId() then
--         aim_test = IsPlayerFreeAiming(player_id)
--       end
--
--       if not aim_test and IsPedInAnyVehicle(player_ped, true) then
--         local _, weapon_hash = GetCurrentPedWeapon(player_ped)
--
--         if weapon_hash and weapon_hash ~= `WEAPON_UNARMED` then
--           SetPedCurrentWeaponVisible(player_ped, false, true)
--           SetPedCurrentWeaponVisible(player_ped, true, true)
--         end
--       end
--     end
--   end
-- end)