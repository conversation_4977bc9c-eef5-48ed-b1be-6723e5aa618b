if Link.framework ~= 'none' and Link.framework ~= 'standalone' then
    return
end

local core = exports.blrp_core
function CanPlayerAfford(player, amount)
  return core:character(player).getCash() >= amount
end

function AddPlayerMoney(player, amount, account)
  return core:character(player).giveCash(player, amount)
end

function RemovePlayerMoney(player, amount)
  return core:character(player).take('cash', amount)
end

function GetPlayerItemData(player, item)
  -- this needs further work, doesnt return correct data currently
  -- designed for QB Core GetItemByName function
  print('GetPlayerItemData', item)
  return exports.blrp_core:GetItemDefinition(item)
end

function GetPlayerItemCount(player, item)
  return core:character(player).getItemQuantity(item)
end

--(item_id, amount, meta, notify, _, callback, item_is_from_storage)
function AddPlayerItem(player, item, amount, meta)
  local given, _, dropped = core:character(player).giveOrDrop(item, amount, meta, true, true)
  if dropped then
    core:character(player).notify("You didn't have room for ".. tostring(amount).." x " .. exports.blrp_core:GetItemName(item) .. " and dropped it on the ground")
  end
  core:character(player).log('METH', 'Item Added by Meth System', {
    item_id = item,
    qty = amount,
    given = given,
    dropped = dropped,
  })
  return given or dropped
end

function RemovePlayerItem(player, item, amount)
  core:character(player).log('METH', 'Item Removed by Meth System', {
    item_id = item,
    qty = amount,
    given = given,
    dropped = dropped,
  })
  return core:character(player).take(item, amount)
end

function GetPlayerCharacterId(player)
  return core:character(player).get('id')
end
