tCore = T.getInstance('blrp_core', 'core')

local dmodality, dispatch_status_codes = exports.blrp_core:GetDispatchModalitiesAndCodes()

local function classIntToStr(c)
  c = tonumber(c)

  if c == 0 then
    return 'COMPACT'
  elseif c == 1 then
    return 'SEDAN'
  elseif c == 2 then
    return 'SUV'
  elseif c == 3 then
    return 'COUPE'
  elseif c == 4 then
    return 'MUSCLE'
  elseif c == 5 or c == 6 then
    return 'SPORT'
  elseif c == 7 then
    return 'SUPER'
  elseif c == 8 then
    return 'MOTOR'
  elseif c == 9 then
    return 'OFFRD'
  elseif c == 10 or c == 11 then
    return 'UTIL'
  elseif c == 12 then
    return 'VAN'
  end

  return 'UNK'
end

local function appendCharacterInfo(data, character_id)
  -- Load data for individual
  local character_info = exports.blrp_tablet:DoPostRequest('/secure/info-extended/' .. character_id)

  -- Append character info
  if character_info then
    -- Append parole time remaining, if applicable
    if character_info.active_parole then
      data.parole = character_info.active_parole.remaining_months
    end

    -- Build wanted crimes string
    local wanted_incidents = {}

    if character_info.incidents_wanted then
      for _, incident in pairs(character_info.incidents_wanted) do
        local incident_crimes_added = {}

        wanted_incidents[incident.police_incident_id] = {}

        for _, crime in pairs(incident.police_crimes) do
          data.wanted = true

          if crime.crime and not incident_crimes_added[string.trim(crime.crime.name)] then
            incident_crimes_added[string.trim(crime.crime.name)] = true
            table.insert(wanted_incidents[incident.police_incident_id], string.trim(crime.crime.name))
          end
        end

        wanted_incidents[incident.police_incident_id] = table.concat(wanted_incidents[incident.police_incident_id], ', ')
      end
    end

    if data.wanted then
      data.wanted_information = wanted_incidents
    end

    local addresses = {}

    for _, house in pairs(character_info.owned_houses or {}) do
      table.insert(addresses, house.address)
    end

    for _, house in pairs(character_info.co_owned_houses or {}) do
      table.insert(addresses, house.address)
    end

    -- Append addresses as comma-separated list
    if #addresses > 0 then
      data.address = table.concat(addresses, ', ')
    end
  end

  -- Append licenses
  local licenses = MySQL.scalar.await('SELECT dvalue FROM vrp_user_data WHERE dkey = ?', {
    'vRP:licenses' .. character_id
  })

  if licenses then
    licenses = json.decode(licenses)

    data.licenses = true

    -- DL status
    data.dl = 'NONE'

    if licenses.driverlicense then
      if tonumber(licenses.driverlicense.licensed) == 1 then
        data.dl = 'ACTIVE'
      end

      if tonumber(licenses.driverlicense.suspended) == 1 then
        data.dl = 'REVOKED'
      end
    end

    -- FL status
    data.fl = 'NONE'

    if licenses.firearmlicense then
      if tonumber(licenses.firearmlicense.licensed) == 1 then
        data.fl = 'ACTIVE'
      end

      if tonumber(licenses.firearmlicense.suspended) == 1 then
        data.fl = 'REVOKED'
      end
    end

    -- CDL status
    data.cdl = 'NONE'

    if licenses.cdl then
      if tonumber(licenses.cdl.licensed) == 1 then
        data.cdl = 'ACTIVE'
      end

      if tonumber(licenses.cdl.suspended) == 1 then
        data.cdl = 'REVOKED'
      end
    end

    -- HNTL status
    data.hntl = 'NONE'

    if licenses.huntinglicense then
      if tonumber(licenses.huntinglicense.licensed) == 1 then
        data.hntl = 'ACTIVE'
      end

      if tonumber(licenses.huntinglicense.suspended) == 1 then
        data.hntl = 'REVOKED'
      end
    end
  end
end

local function getPlateQueryResponse(plate)
  local data = MySQL.single.await([[
    SELECT
      v.registration, v.business, v.vehicle, v.colour, v.scolour, v.colour_rgb, v.characterNumber,
      c.firstname, c.lastname, c.phone, c.dlnumber
    FROM vrp_user_vehicles v
      LEFT JOIN characters c ON c.id = v.characterNumber
      WHERE v.registration = ?
  ]], {
    plate
  }) or {}

  if data and data.vehicle then
    data.vehicle = string.upper(data.vehicle)
    data.make = exports.blrp_core:GetVehicleMakeFromName(data.vehicle)
    data.class = classIntToStr(exports.blrp_core:GetVehicleClassFromName(data.vehicle))

    -- TODO: color

    if data.business then
      -- Load data for business
      -- TODO: phone, address
    elseif data.characterNumber and tonumber(data.characterNumber) > 0 then
      appendCharacterInfo(data, data.characterNumber)
    end
  end

  -- Failover for no results
  data.query_type = 'vehicle'
  data.query = plate
  data.success = (data.vehicle and true or false)

  -- TODO: BOLO for vehicle

  return data
end

local commands = {
  ['PL'] = {
    log_direct = true,
    callback = function(executing_character, callsigns, args)
      args = string.trim(args or '')

      if args == '' then
        return false, 'No plate provided'
      end

      local data = getPlateQueryResponse(args)

      -- Reframe data for front end
      data = {
        mail = {
          [args] = data
        }
      }

      return true, data
    end,
  },

  ['RA'] = {
    log_direct = true,
    callback = function(executing_character, callsigns, args)
      for _, v in pairs(callsigns) do
        local character_id, callsign = table.unpack(v)

        local character = exports.blrp_core:characterFromId(character_id)

        if character then
          local dispatch_status = character.get('dispatch_status')

          if dispatch_status then
            character.set('dispatch_status', {
              code = dispatch_status.code,
              time = os.time(),
              message = dispatch_status.message,
              call_number = dispatch_status.call_number,
            })
          end
        end
      end

      return true, {}
    end,
  },

  -- Query firearm registration
  ['QFR'] = {
    callback = function(executing_character, callsigns, args)
      args = string.trim(args or '')

      if args == '' then
        return false, 'No registration # provided'
      end

      local data = MySQL.single.await([[
        SELECT er.weapon_name, er.registration, er.character_number, c.firstname, c.lastname, c.phone, c.dlnumber FROM evidence_registrations er
          JOIN characters c ON c.id = er.character_number
          WHERE er.registration = ?
      ]], {
        args
      }) or {}

      -- Failover for no results
      data.query_type = 'firearm'
      data.query = args
      data.success = (data.weapon_name and true or false)

      if data.character_number then
        appendCharacterInfo(data, data.character_number)
      end

      -- Reframe data for front end
      data = {
        mail = {
          [args] = data
        }
      }

      return true, data
    end
  },

  -- Query drivers license
  ['QDL'] = {
    callback = function(executing_character, callsigns, args)
      args = string.trim(args or '')

      if args == '' then
        return false, 'No DL # provided'
      end

      local data = MySQL.single.await([[
        SELECT c.id, c.firstname, c.lastname, c.phone, c.dlnumber FROM characters c
          WHERE c.dlnumber = ?
      ]], {
        args
      }) or {}

      -- Failover for no results
      data.query_type = 'driverlicense'
      data.query = args
      data.success = (data.id and true or false)

      if data.id then
        appendCharacterInfo(data, data.id)
      end

      -- Reframe data for front end
      data = {
        mail = {
          [args] = data
        }
      }

      return true, data
    end
  },

  -- Query plate
  ['QPL'] = {
    callback = function(executing_character, callsigns, args)
      args = string.trim(args or '')

      if args == '' then
        return false, 'No plate provided'
      end

      local data = getPlateQueryResponse(args)

      -- Reframe data for front end
      data = {
        mail = {
          [args] = data
        }
      }

      return true, data
    end
  },

  -- Query person
  ['QPERS'] = {
    callback = function(executing_character, callsigns, args)
      args = string.trim(args or '')

      if not args or args == '' then
        return false, 'No name provided to search'
      end

      if not string.find(args, '%s') then
        return false, 'Search term must contain first and last name'
      end

      local data = exports.blrp_tablet:DoPostRequest('/secure/dispatch-qpers', {
        search = args
      }) or { success = false, results = {} }

      -- Reframe data for front end
      if data.success then
        data.query = args
      end

      exports.blrp_core:print_r(data)

      data = {
        mail = {
          [args] = data
        }
      }

      return true, data
    end
  },

  -- Route units 2+ to first unit
  ['ROUTE'] = {
    requires_callsigns = true,
    callsign_limit = 2,
    callback = function(executing_character, callsigns, args)
      if #callsigns < 2 then
        return false, '2+ callsigns required'
      end

      local source_character = exports.blrp_core:characterFromId(callsigns[1][1])
      local source_coords = source_character.getCoordinates()

      for i = 2, #callsigns do
        local character = exports.blrp_core:characterFromId(callsigns[i][1])

        tCore.setGps(character.source, { source_coords })
        character.notify('Dispatch has routed you to ' .. callsigns[1][2])
      end

      return true, 'Units routed to ' .. callsigns[1][2]
    end
  },

  -- Set channel
  ['CHAN'] = {
    requires_callsigns = true,
    callback = function(executing_character, callsigns, args)
      local channel = tonumber(args)

      if not channel or channel <= 0 or channel > 100 or math.floor(channel) ~= channel then
        return false, 'Invalid channel: ' .. args
      end

      local valid_channels = exports.blrp_radio:GetNamedChannels()

      if not valid_channels[channel] then
        return false, 'Invalid channel: ' .. args
      end

      -- Iterate characters and set radio channel
      for _, v in pairs(callsigns) do
        local character_id, callsign = table.unpack(v)

        for src, _character in pairs(exports.blrp_core:GetOnlineCharacters()) do
          if tonumber(_character.id) == character_id then
            local character = exports.blrp_core:character(src)

            character.notifyError('Dispatch set your radio channel to: ' .. channel)
            TriggerEvent('blrp_radio:server:tryJoinChannel', src, channel, true)
          end
        end
      end

      return true, {}
    end
  },

  -- Set status (abstract command for all status codes)
  ['STATUS'] = {
    requires_callsigns = true,
    callback = function(executing_character, callsigns, args, status_code)
      local code_long, self_assign, requires_remarks, busy, carry_call_number, code_modality = table.unpack(dispatch_status_codes[status_code])

      -- If code requires remarks, ensure remark are provided
      if requires_remarks and (not args or args == '') then
        return false, 'Remarks required for ' .. status_code
      end

      -- Verify status code is permitted for each callsign
      for _, v in pairs(callsigns) do
        local character_id, callsign = table.unpack(v)

        for src, _character in pairs(exports.blrp_core:GetOnlineCharacters()) do
          if tonumber(_character.id) == character_id then
            local dispatch_modality = exports.blrp_core:GetDispatchModality(_character)

            -- Check if this code can be used for this person
            if
              code_modality ~= dmodality.ALL and
              dispatch_modality & code_modality ~= dispatch_modality
            then
              return false, 'Code ' .. status_code .. ' not permitted for ' .. callsign
            end
          end
        end
      end

      local response = {}

      -- Run plates, if any found
      for plate in string.gmatch(args, "@(%w+)") do
        if not response.mail then
          response.mail = {}
        end

        response.mail[plate] = getPlateQueryResponse(plate)
      end

      -- Iterate characters and set status
      for _, v in pairs(callsigns) do
        local character_id, callsign = table.unpack(v)

        for src, _character in pairs(exports.blrp_core:GetOnlineCharacters()) do
          if tonumber(_character.id) == character_id then
            local character = exports.blrp_core:character(src)

            local appends = ''

            if args and args ~= '' then
              appends = ' - ' .. args
            end

            character.notifyError('Dispatch set your status to: ' .. status_code .. appends)
            character.set('dispatch_status', {
              code = status_code,
              time = os.time(),
              message = args,
            })
          end
        end
      end

      return true, response
    end
  },
}

function extractParts(part_type, parts, limit)
  local extracted = {}
  local new_parts = {}

  local command_found = false

  if not limit then
    limit = 1000
  end

  for _, v in pairs(parts) do
    local found = false

    if not command_found and part_type == 'command' then
      if commands[v] or dispatch_status_codes[v] then
        command_found = true
        found = true

        if dispatch_status_codes[v] then
          table.insert(extracted, 'STATUS')
        end

        table.insert(extracted, v)
      end
    elseif part_type == 'callsign' then
      for src, _character in pairs(exports.blrp_core:GetOnlineCharacters()) do
        local callsign_base = string.lower(tostring(_character.callsign or ''))
        local callsign_numeric = string.match(callsign_base, '%d+') or false
        local callsign_search = string.lower(tostring(v))

        if
          #extracted < limit and
          not string.match(callsign_base, 'VW') and
          (
            callsign_base == callsign_search or
            tostring(callsign_numeric) == callsign_search
          )
        then
          found = true
          table.insert(extracted, { tonumber(_character.id), v })
        end
      end
    end

    if not found then
      table.insert(new_parts, v)
    end
  end

  return extracted, new_parts
end

function executeDispatchCommand(vrp_id, input)
  local character = exports.blrp_core:characterFromVrp(vrp_id)

  if not character or not character.hasGroup('Dispatch') then
    -- TODO: dev - uncomment -- return false, 'Unauthorized action. Are you clocked in as Dispatch?'
  end

  input = string.upper(input)

  local parts = string.split(input, ' ')
  local extracted, parts = extractParts('command', parts)
  local command, status_code = table.unpack(extracted)

  if not command then
    return false, 'Invalid command specified: ' .. input
  end

  local command_def = commands[command]

  if not command_def then
    return false, 'Invalid command specified: ' .. input
  end

  local callsigns, args = extractParts('callsign', parts, command_def.callsign_limit)
  args = string.trim(table.concat(args, ' '))

  print('-> raw dispatch command = ' .. input)
  print('-> command = ' .. command .. ' (' .. (status_code or '') .. ')')
  print('-> callsigns = ' .. #callsigns)
  print('-> args = ' .. args)

  -- If command requires callsign and no callsign provided, error
  if command_def.requires_callsigns and #callsigns == 0 then
    return false, 'Invalid callsign specified'
  end

  local success, message, characters_affected = command_def.callback(character, callsigns, args, status_code)

  if success then
    character.log('DISPATCH-COMMAND', input)

    -- Status history log per character affected (database)
    if #callsigns > 0 then
      local dispatcher_id = character.get('id')
      local dispatcher_name = character.get('fullname')

      for _, callsign in pairs(callsigns) do
        local callsign_character_id = callsign[1]
        local callsign_character = exports.blrp_core:characterFromId(callsign_character_id)
        local callsign_status = callsign_character.get('dispatch_status')

        local log_call = false
        local log_code = false
        local log_data = false

        if
          callsign_status and
          callsign_status.call_number and
          callsign_status.call_number.agency and
          callsign_status.call_number.year and
          callsign_status.call_number.number
        then
          log_call = callsign_status.call_number.agency .. callsign_status.call_number.year .. '-' .. callsign_status.call_number.number
        end

        if command == 'STATUS' then
          log_code = status_code
          log_data = args
        elseif command_def.log_direct then
          log_code = command
          log_data = args
        end

        if log_code or log_data then
          MySQL.insert([[
            INSERT INTO core_status_history
              (`character_id`, `dispatcher_id`, `dispatcher_name`, `call`, `code`, `data`) VALUES
              (?, ?, ?, ?, ?, ?)
          ]], {
            callsign_character_id, dispatcher_id, dispatcher_name, (log_call or ''), (log_code or ''), (log_data or '')
          })
        end
      end
    end
  end

  return success, message
end
