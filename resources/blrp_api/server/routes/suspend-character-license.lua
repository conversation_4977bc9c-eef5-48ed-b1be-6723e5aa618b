routes['/suspend-character-license'] = {
  method = 'POST',
  callback = function(request, api_account)

    local postdata = validateRequest(request, {
      'leo_vrp_id', 'suspect_vrp_id', 'license_type'
    })

    if not postdata then
      return 400, { }
    end

    -- Assumes they are online
    local leo_character = exports.blrp_core:characterFromVrp(postdata.leo_vrp_id)
    local suspect_character = exports.blrp_core:characterFromVrp(postdata.suspect_vrp_id)

    if not leo_character or not suspect_character then
      leo_character.notify('You are not close enough to this person')
      return 400, {}
    end

    local distance = leo_character.distanceFrom(suspect_character.getCoordinates())

    if tonumber(distance) > 15 then
      leo_character.notify('You are not close enough to this person.')
      return 400, {}
    end

    leo_character.log('REVOKED_LICENSE', 'Revoked ' .. postdata.license_type .. ' from ' .. suspect_character.get('identifier') .. ' / ' .. suspect_character.get('firstname') .. ' ' .. suspect_character.get('lastname'))

    local suspect_vrp_id = tonumber(suspect_character.get('identifier'))
    local license_type = postdata.license_type

    TriggerEvent('vrp:server:suspendPlayerLicense', suspect_vrp_id, license_type)

    return 200, { success = true }
  end
}
