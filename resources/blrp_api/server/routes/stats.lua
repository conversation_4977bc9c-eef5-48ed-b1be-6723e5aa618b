routes['/stats'] = {
  method = 'GET',
  callback = function(request, api_account)
    local user_tables = exports.blrp_core:GetUserTables()
    local source_identifier = user_tables.source_identifier
    local identifier_character = user_tables.identifier_character
    local hidden_users = exports.blrp_core:GetHiddenUsers()

    local sources = GetPlayers()

    local players = {}
    local characters = {}

    for _, player in pairs(sources) do
      player = tonumber(player)

      local identifier = source_identifier[player]

      if identifier and not hidden_users[identifier] then
        table.insert(players, identifier)

        local character_id = identifier_character[identifier]

        if character_id then
          table.insert(characters, character_id)
        end
      end
    end

    -- Faction presence
    local groups = {
      ['staff'] = true,
      ['Police'] = true,
      ['NYSP'] = true,
      ['Lawyer'] = true,
      ['DOC'] = true,
      ['Lifer'] = true,
      ['DOJ'] = true,
      ['Prisoner'] = true,
      ['LSFD'] = true,
    }

    local faction_presence = {}

    local online_characters = exports.blrp_core:GetOnlineCharacters()

    for group_category, group_definition in pairs(groups) do
      local group_names = nil

      if type(group_definition) == 'table' then
        group_names = group_definition
      else
        group_names = { group_category }
      end

      faction_presence[group_category] = {}

      for _, group_name in pairs(group_names) do
        for _, character in pairs(online_characters) do
          if not character.esp_hidden and character.groups and character.groups[group_name] then
            local character_data = {
              callsign = character.callsign or '',
              firstname = character.firstname,
              lastname = character.lastname,
              leo_status = character.dispatch_status,
            }

            if group_category == 'staff' then
              character_data.vrp = character.identifier
              character_data.esp_active = character.esp_active
              character_data.god_active = character.god_active
              character_data.noclip_active = character.noclip_active
            end

            table.insert(faction_presence[group_category], character_data)
          end
        end
      end

      table.sort(faction_presence[group_category], function(a, b)
        return tostring(a.callsign) < tostring(b.callsign)
      end)
    end
    -- / Faction presence

    return 200, {
      players = players,
      characters = characters,
      counts = {
        players = #sources,
        playersMax = GetConvarInt('sv_maxclients', 200),
        queue = exports.pQueue:GetQueueSize(),
      },
      faction_presence = faction_presence,
    }
  end
}
