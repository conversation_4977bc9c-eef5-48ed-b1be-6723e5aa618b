<?xml version="1.0" encoding="UTF - 8"?>
<CWeaponComponentInfoBlob>
	<Infos>
		<Item type="CWeaponComponentClipInfo">
			<Name>COMPONENT_VP897_CLIP_01</Name>
			<Model>w_pi_vp897_mag1</Model>
			<LocName>WCT_CLIP1</LocName>
			<LocDesc>WCD_P_CLIP1</LocDesc>
			<AttachBone>AAPClip</AttachBone>
			<WeaponAttachBone>WAPClip</WeaponAttachBone>
			<AccuracyModifier type="NULL"/>
			<DamageModifier type="NULL"/>
			<bShownOnWheel value="false"/>
			<CreateObject value="true"/>
			<HudDamage value="0"/>
			<HudSpeed value="0"/>
			<HudCapacity value="0"/>
			<HudAccuracy value="0"/>
			<HudRange value="0"/>
			<ClipSize value="7"/>
			<AmmoInfo>AMMO_PISTOL_FMJ</AmmoInfo>
			<ReloadData ref="RELOAD_DEFAULT_WITH_EMPTIES"/>
		</Item>
		<Item type="CWeaponComponentClipInfo">
			<Name>COMPONENT_VP897_CLIP_02</Name>
			<Model>w_pi_vp897_mag2</Model>
			<LocName>WCT_CLIP2</LocName>
			<LocDesc>WCD_P_CLIP2</LocDesc>
			<AttachBone>AAPClip</AttachBone>
			<WeaponAttachBone>WAPClip</WeaponAttachBone>
			<AccuracyModifier type="NULL"/>
			<DamageModifier type="NULL"/>
			<bShownOnWheel value="true"/>
			<CreateObject value="true"/>
			<HudDamage value="0"/>
			<HudSpeed value="0"/>
			<HudCapacity value="33"/>
			<HudAccuracy value="0"/>
			<HudRange value="0"/>
			<ClipSize value="22"/>
			<AmmoInfo>AMMO_PISTOL_FMJ</AmmoInfo>
			<ReloadData ref="RELOAD_LARGE_WITH_EMPTIES"/>
		</Item>
		<Item type="CWeaponComponentSuppressorInfo">
			<Name>COMPONENT_AT_VP897_SUPP</Name>
			<Model>w_at_pi_vet_supp</Model>
			<LocName>WCT_SUPP</LocName>
			<LocDesc>WCD_PI_SUPP</LocDesc>
			<AttachBone>AAPSupp</AttachBone>
			<WeaponAttachBone>WAPSupp</WeaponAttachBone>
			<AccuracyModifier type="NULL"/>
			<DamageModifier type="CWeaponDamageModifier">
				<DamageModifier value="1.000000"/>
			</DamageModifier>
			<bShownOnWheel value="true"/>
			<CreateObject value="true"/>
			<HudDamage value="-5"/>
			<HudSpeed value="0"/>
			<HudCapacity value="0"/>
			<HudAccuracy value="0"/>
			<HudRange value="-5"/>
			<MuzzleBone>Gun_SuMuzzle</MuzzleBone>
			<FlashFx>muz_pistol_silencer</FlashFx>
		</Item>
	</Infos>
	<InfoBlobName/>
</CWeaponComponentInfoBlob>

