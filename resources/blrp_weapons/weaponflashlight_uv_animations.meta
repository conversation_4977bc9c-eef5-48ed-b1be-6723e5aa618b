<?xml version="1.0" encoding="UTF-8"?>
<CWeaponAnimationsSets>
  <WeaponAnimationsSets>
    <Item key="Default">
      <WeaponAnimations>
        <Item key="WEAPON_FLASHLIGHT_UV">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Pistol</CoverWeaponClipSetHash>
          <MotionClipSetHash>weapons@melee_1h</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash>move_strafe_melee_unarmed</MotionStrafingClipSetHash>
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash>move_strafe@melee_small_weapon</MotionStrafingUpperBodyClipSetHash>
          <WeaponClipSetHash>anim@weapons@flashlight@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>anim@weapons@flashlight@stealth</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@small_wpn@streamed_core</MeleeClipSetHash>
          <MeleeVariationClipSetHash>melee@hatchet@streamed_core</MeleeVariationClipSetHash>
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>move_jump@weapons@1h_melee@hammer</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="0.000000" />
          <AnimBlindFireRateModifier value="0.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPerson">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_FLASHLIGHT_UV">
        <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
        <CoverMovementClipSetHash />
        <CoverMovementExtraClipSetHash />
        <CoverAlternateMovementClipSetHash />
        <CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
        <MotionClipSetHash>weapons@first_person@aim_idle@generic@melee@knife@shared@core</MotionClipSetHash>
        <MotionFilterHash>BothArms_filter</MotionFilterHash>
        <MotionCrouchClipSetHash />
        <MotionStrafingClipSetHash>move_strafe_melee_unarmed_fps</MotionStrafingClipSetHash>
        <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
        <MotionStrafingUpperBodyClipSetHash>move_strafe@melee_knife_fps</MotionStrafingUpperBodyClipSetHash>
        <WeaponClipSetHash>weapons@first_person@aim_idle@generic@melee@knife@shared@core</WeaponClipSetHash>
        <WeaponClipSetStreamedHash />
        <WeaponClipSetHashInjured />
        <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@melee@knife@</WeaponClipSetHashStealth>
        <WeaponClipSetHashHiCover />
        <AlternativeClipSetWhenBlocked />
        <ScopeWeaponClipSet />
        <AlternateAimingStandingClipSetHash />
        <AlternateAimingCrouchingClipSetHash />
        <FiringVariationsStandingClipSetHash />
        <FiringVariationsCrouchingClipSetHash />
        <AimTurnStandingClipSetHash />
        <AimTurnCrouchingClipSetHash />
        <MeleeClipSetHash>melee@knife@streamed_core_fps</MeleeClipSetHash>
        <MeleeVariationClipSetHash>melee@knife@streamed_variations</MeleeVariationClipSetHash>
        <MeleeTauntClipSetHash />
        <MeleeSupportTauntClipSetHash />
        <MeleeStealthClipSetHash />
        <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
        <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@1H_MELEE@KNIFE</JumpUpperbodyClipSetHash>
        <FallUpperbodyClipSetHash />
        <FromStrafeTransitionUpperBodyClipSetHash />
        <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
        <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
        <AnimFireRateModifier value="0.000000" />
        <AnimBlindFireRateModifier value="0.000000" />
        <AnimWantingToShootFireRateModifier value="-1.000000" />
        <UseFromStrafeUpperBodyAimNetwork value="false" />
        <AimingDownTheBarrel value="true" />
        <WeaponSwapData ref="SWAP_DEFAULT" />
        <AimGrenadeThrowNormalClipsetHash />
        <AimGrenadeThrowAlternateClipsetHash />
        <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@melee@knife@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
        <FPSTransitionFromLTHash>weapons@first_person@aim_idle@p_m_zero@melee@knife@aim_trans@unholster_to_idle</FPSTransitionFromLTHash>
        <WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@melee@one_handed@shared@core</WeaponClipSetHashForClone>
        <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_idle@p_m_zero@melee@knife@fidgets@a</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@melee@knife@fidgets@b</Item>
            <Item>weapons@first_person@aim_idle@p_m_zero@melee@knife@fidgets@c</Item>
        </FPSFidgetClipsetHashes>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPersonAiming">
      <Fallback>Default</Fallback>
    </Item>
    <Item key="FirstPersonRNG">
      <Fallback>Default</Fallback>
    </Item>
    <Item key="FirstPersonScope">
      <Fallback>Default</Fallback>
    </Item>
  </WeaponAnimationsSets>
</CWeaponAnimationsSets>
