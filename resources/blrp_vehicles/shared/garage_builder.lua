garages = {}

SetTimeout(1000, function()
  -- Handle garages defined with vector4 coords
  for id, def in pairs(config_garage_locations) do
    if type(def.coords) == 'vector4' then
      def.heading = def.coords.w
      def.coords = def.coords.xyz
    end

    -- Safe grid calculation for Lua 5.4 compatibility
    local grid = GetGridAtCoords(def.coords, 50)
    if grid and grid.x and grid.y then
      def.grid = tostring(math.floor(grid.x)) .. "," .. tostring(math.floor(grid.y))
    else
      def.grid = "0,0"
    end
  end

  computed_garage_locations = config_garage_locations

  Citizen.CreateThread(function()
    local impound_computed = {
      category = 'y',
      vehicles = {}
    }

    local impound_computed_flat = {}

    for category_name, category_data in pairs(config_vehicles.categories) do
      if not impound_computed.vehicles[category_name] then
        impound_computed.vehicles[category_name] = {}
      end

      for vehicle_name, vehicle_data in pairs(category_data) do
        if vehicle_name ~= '_config' then
          local vehicle_def = {
            [1] = vehicle_data[1],
            [2] = vehicle_data[2],
            [3] = vehicle_data[3],
            [4] = category_name,
            permission_check = vehicle_data[4],
          }

          impound_computed.vehicles[category_name][vehicle_name] = vehicle_def
          impound_computed_flat[vehicle_name] = true
          config_vehicles_all[vehicle_name] = vehicle_def
        end
      end
    end

    -- local fees = {}

    -- for a, b in pairs(config_vehicles_all) do
    --   local price = b[2]

    --   if price and price > 0 and price < 100000000 and not ({
    --     ['gtf'] = true,
    --     ['cru'] = true,
    --   })[b[4]] then
    --     local fee_config = array(config_garage_fees.category_specific):find(function(f)
    --       return f[1] == b[4]
    --     end)

    --     local rate = config_garage_fees.base_rate
    --     local cap = 100000

    --     if fee_config then
    --       rate = fee_config[2]
    --       cap = fee_config[3]
    --     end

    --     local fee = math.min(cap, math.floor(b[2] * rate))

    --     table.insert(fees, { b[1], fee, b[4] })
    --   end
    -- end

    -- table.sort(fees, function(a, b)
    --   return a[2] < b[2]
    -- end)

    -- for k, v in pairs(fees) do
    --   print('[' .. v[3] .. '] ' .. v[1] .. ': $' .. math.comma(v[2]))
    -- end

    for impound_id, impound_data in pairs(config_impounds.locations) do
      config_garages_computed[impound_id] = impound_computed
      config_garages_computed_flat[impound_id] = impound_computed_flat
    end
  end)

  Citizen.CreateThread(function()
    for garage_id, garage_config in pairs(config_garage_locations) do
      local category_type = string.sub(garage_id, 1, 1)
      local category_config = config_garage_categories[category_type]

      config_garages_computed_flat[garage_config.id] = {}

      if category_config then
        local _garage = {
          category = category_type,
          vehicles = {}
        }

        for category_name, category_items in pairs(category_config) do
          local category_vehicles = config_vehicles.categories[category_name]

          if category_vehicles then
            if not _garage.vehicles[category_name] then
              _garage.vehicles[category_name] = {}
            end

            if type(category_items) == 'boolean' and category_items == true then
              -- Add all vehicles in category

              _garage.vehicles[category_name] = category_vehicles

              for _vehicle_name, __ in pairs(category_vehicles) do
                if _vehicle_name ~= 'config' then
                  config_garages_computed_flat[garage_config.id][_vehicle_name] = true
                end
              end
            elseif type(category_items) == 'table' then
              if category_items.only then
                for vehicle_name, vehicle_config in pairs(category_vehicles) do
                  if vehicle_name ~= '_config' then
                    for __, only_vehicle_name in ipairs(category_items.only) do
                      if vehicle_name == only_vehicle_name then
                        _garage.vehicles[category_name][vehicle_name] = vehicle_config
                        config_garages_computed_flat[garage_config.id][vehicle_name] = true
                      end
                    end
                  end
                end
              elseif category_items.remove then
                local _remove = {}

                for __, remove_vehicle_name in ipairs(category_items.remove) do
                  _remove[remove_vehicle_name] = true
                end

                for vehicle_name, vehicle_config in pairs(category_vehicles) do
                  if vehicle_name ~= '_config' then
                    if not _remove[vehicle_name] then
                      _garage.vehicles[category_name][vehicle_name] = vehicle_config
                      config_garages_computed_flat[garage_config.id][vehicle_name] = true
                    end
                  end
                end
              else
                print('-------------- NO INCLUSION OPTION FOR CATEGORY', garage_id, category_type, category_name)
              end
            else
              print('-------------- INVALID VALUE FOR CATEGORY TYPE CATEGORY', garage_id, category_type, category_name, category_items)
            end
          else
            print('-------------- NO CATEGORY DEF FOR CATEGORY NAME', category_name)
          end
        end

        config_garages_computed[garage_config.id] = _garage
      else
        print('-------------- NO CATEGORY DEF FOR CATEGORY TYPE', category_type)
      end
    end

    config_garages_computed['z1'] = config_garages_computed['a1']
    config_garages_computed_flat['z1'] = config_garages_computed_flat['a1']
  end)
end)
