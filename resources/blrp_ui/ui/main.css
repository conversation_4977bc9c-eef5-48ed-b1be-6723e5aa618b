* {
    padding: 0;
    margin: 0;
}

label {
    margin-bottom: 7px !important;
}
.root-wrapper {
    width: 115vw;
    height: auto;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    position: absolute;
    overflow: hidden;
    background: transparent !important;
    overflow-x: hidden;
}

.form-timer {
    font-size: 16px;
    font-weight: bold;
    color: #f56;
    margin-left: 20px;
}

.prompts-wrapper {
    position: absolute;
    right: 180px;
}

.prompt {
    font-size: 14px;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 7px;
    background-color: #171717;
    color: whitesmoke;
    border-radius: 8px;
}

html,body {
    background: transparent !important;
    overflow-x: hidden;
}

.main-wrapper {
    width: 35%;
    height: auto;
    max-height: 85vh;
    background: #151515;
    margin: auto;
    position: relative;
    top: 10%;
    overflow: hidden;
    border-radius: 3%;
    padding: 1.5rem;
    display: none;
}

.main-wrapper:not([style*="display: none"]) {
    display: flex !important;
    flex-direction: column;
}

.heading {
    width: 100%;
    max-width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    position: relative;
    justify-content: center;
    overflow: wrap;
}

.heading h1 {
    color: white;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 0.85rem;
    font-weight: bold;
    position: relative;
}

.body {
    width: 100%;
    flex: 1;
    min-height: 0;
    margin: auto;
    display: grid;
    font-size: 0.8rem;
    font-weight: bold;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.body label {
    color: white;
    margin-top: 0.8em;
    margin-bottom: 0.3em;
    display: block;
}

/* Grid item styling */
.body > div {
    display: flex;
    flex-direction: column;
}

.body > div label {
    margin-bottom: 0.25rem;
}

.body input[type="text"] {
    background: #1f2533;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 0.25rem;
    margin-top: 0.25rem;
}

*:disabled {
    background-color: rgba(86, 86, 86, 0.32) !important;
    color: linen;
    opacity: 1;
}

/* Header styling for disabled fields that start with ── */
input[disabled][id^="header_"] {
    background: transparent !important;
    border: none !important;
    text-align: center !important;
    font-weight: bold !important;
    color: #60a5fa !important;
    font-size: 0.9rem !important;
    margin-top: 1rem !important;
    margin-bottom: 0.5rem !important;
    padding: 0.5rem 0 !important;
    box-shadow: none !important;
    cursor: default !important;
}

/* Hide labels for header fields */
label[for^="header_"] {
    display: none !important;
}

/* Container for header fields should span full width */
.header-field {
    grid-column: 1 / -1 !important;
}

.body input[type="text"]:focus {
    box-shadow: 0 0 0 0.1rem#1f2533;
}

.body select {
    background: #1f2533;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 0.25rem;
    margin-top: 0.25rem;
}


.body option {
    background: #1f2533;
    color: white;
    padding: 1rem 0;
}

.footer {
    flex-shrink: 0;
    display: flex;
    justify-content: end;
    align-items: center;
    margin-top: 25px;
    margin-right: 20px;
}

.btn {
    background-color: greenyellow;
    padding: .175rem 1rem;
}


.background {
    width: auto;
    height: auto;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(35, 35, 35, 0.45);
    z-index: -99;
    display: none;
}

.select2-instance {
    color: white;
    border-color: #1F2533;
    background-color: #1F2533;
    box-shadow: 0 0 2px 2px #222222 inset;
}

li {
  color: white;
  margin-left: 10px;
  list-style: disc;
}

*:not(input) {
  user-select: none;
}

/* Custom scrollbar styling for the body */
.body::-webkit-scrollbar {
    width: 8px;
}

.body::-webkit-scrollbar-track {
    background: #1f2533;
    border-radius: 4px;
}

.body::-webkit-scrollbar-thumb {
    background: #60a5fa;
    border-radius: 4px;
}

.body::-webkit-scrollbar-thumb:hover {
    background: #3b82f6;
}
