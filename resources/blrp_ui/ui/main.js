let fields = [];
let selects = []
let Rows = [];
let saved = "";
const promptIntervals = {}

/**
 * Prompts
 */

function addPrompt(prompt) {
    const message = prompt.message
    const timeout = prompt.timeout
    const identifier = prompt.identifier
    let html = null

    if ( prompt.question ) {
        html = `<div class="accept" id="prompt-${ identifier }">

      <div class="bkg-cover"></div>

      <span style="margin-left:20px;">
        ${ message }
      </span>

      <span class="buttons">
        <div id="prompt-${identifier}-btn" class="">
        <span>&#x2713;</span>
        <span>[F1]</span>
        </div>
        <div id="prompt-${identifier}-btn2" class="">
        <span>&#x2713;</span>
        <span>[F2]</span>
        </div>
      </span>

      <span style="margin-left:20px; margin-right:10px;" class="remaining-time mr-5" id="prompt-time-${ identifier }">
        ${ timeout }s
      </span>

    </div>`
    } else {
        html = `<div class="accept" id="prompt-${ identifier }">
                  <div class="bkg-cover"></div>

                  <span>
                    ${ message }
                  </span>
                </div>`
    }

    $('#prompts').append(html)

    let remainingTime = timeout

    promptIntervals[identifier] = setInterval(() => {
        remainingTime = remainingTime - 1
        $(`#prompt-time-${ identifier }`).html(
            remainingTime + 's',
        )
    }, 1000)
}

function removePrompt(identifier) {
    $(`#prompt-${ identifier }`).animate({ "margin-right": '-=400' })
        .after(() => {
            $(`#prompt-${ identifier }`).remove()
        })

    if ( promptIntervals[identifier] ) {
        clearInterval(promptIntervals[identifier])
    }
}

function notifyPromptResult(data) {
    const identifier = data.identifier;
    const wasAccepted = data.accepted;

    if (wasAccepted) {
        $(`#prompt-${identifier}-btn`).css("background-color", "green");

    } else {
        $(`#prompt-${identifier}-btn2`).css("background-color", "red");

    }
}


/**
 * Forms
 */

function SetHeader(header) {
    var element
    element = $('<h1>' + header + '<h1>');
    $('.heading').append(element);
    saved = element
}

function addField(data) {
    fields = data
    selects = []

    for (const set of fields) {
        // console.log('set', set)
        let message = set.txt
        let id = set.id
        let options = set.options
        let existingValue = set.value
        let isDisabled = set.disabled ? 'disabled' : ''
        let element

        let style = ''

        if(set.grid_column_start) {
          style += "grid-column-start: " + set.grid_column_start;
        }

        if( set.list && options ) {
          const list = $(`<ul></ul>`);

          if(set.grid_column_start) {
            list.css('grid-column-start', set.grid_column_start);
          }

          $.each(options, function(_, text) {
            list.append($('<li></li>').html(text));
          });

          $('.body').append($(`<label>${ message }</label>`));
          $('.body').append(list);
        } else if ( options ) {
            element = $(`<div><label for="${ id }">${ message }</label> <select ${ isDisabled } style="${ style }" type="text" class="" id="${ id }" /></div>`);

            if(set.grid_column_start) {
                element.css('grid-column', set.grid_column_start);
            }

            $('.body').append(element);
            const selector = $(`#${ id }`)
            $.each(options, function (val, text) {
              let actualValue;

              if(Array.isArray(options)) {
                // Default: use text for backwards compatibility
                // Only use index if explicitly requested
                if(set.use_index_values) {
                  actualValue = val; // Use index (opt-in)
                } else {
                  actualValue = text; // Use text (default, backwards compatible)
                }
              } else {
                actualValue = val; // For objects, val is the key
              }

              selector.append(
                $('<option></option>').val(actualValue).html(text),
              );
            })

            if ( options.length > 20 ) {
                selector.addClass('select2-instance')
                setTimeout(() => {
                    const selectize = selector.selectize()
                    selects.push(selectize);

                    selectize[0].selectize.setValue(existingValue)
                }, 100)
            } else {
                // Set the value for regular dropdowns
                setTimeout(() => {
                    if ( existingValue !== undefined && existingValue !== null ) {
                        // For array-based options, use the index
                        if (Array.isArray(options)) {
                            selector.val(existingValue)
                        } else {
                            // For object-based options, use the key
                            selector.val(existingValue)
                        }
                    }
                }, 100)
            }
        } else {
            element = $(`<div><label for="${ id }">${ message }</label> <input ${ isDisabled } style="${ style }" type="text" class="disabled:bg-gray-300 shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="${ id }" /></div>`);

            if(set.grid_column_start) {
                element.css('grid-column', set.grid_column_start);
            }
        }

        $('.body').append(element);
        Rows[set.id] = element

        const addedElement = $(`#${ set.id }`)

        // Add header class for styling
        if ( set.disabled && set.id.startsWith('header_') ) {
            element.addClass('header-field');
        }

        setTimeout(() => {
            if ( existingValue !== undefined && addedElement ) {
                addedElement.val(existingValue)
            }

            // For header fields, set the text as the value
            if ( set.disabled && set.id.startsWith('header_') && addedElement ) {
                addedElement.val(message)
            }
        }, 200)

    }
}

$(`#submit`).click(() => {
    submitForm();
})

function submitForm() {
    const results = {}
    for (const set of fields) {
        var id = set.id
        var data = document.getElementById(id)

        if ( set.list ) {
          continue;
        }

        // Skip header fields in form submission
        if ( set.disabled && set.id.startsWith('header_') ) {
          continue;
        }

        if ( data && data.value !== undefined && data.value !== '' ) {
            results[id] = data.value
        } else {
            results[id] = null
        }
    }

    PostData({
        data: results,
    })
    CloseMenu();
}

const PostData = (data) => {
    return $.post(`https://blrp_ui/dataPost`, JSON.stringify(data))
}

window.addEventListener("message", (evt) => {
    const data = evt.data
    const info = data.data
    const action = data.action
    switch ( action ) {
        case "OPEN_MENU":
            return OpenMenu(info);
        case "PROMPTS_ADD":
            return addPrompt(info);
        case "PROMPTS_REMOVE":
            return removePrompt(info);
        case "PROMPTS_NOTIFY":
            return notifyPromptResult(info);
        case "CLOSE_MENU":
            return CloseMenu();
        default:
            return;
    }
})

document.onkeyup = function (event) {
    event = event || window.event;
    var charCode = event.keyCode || event.which;
    if ( charCode == 27 ) {
        CancelMenu();
    } else if ( charCode == 13 ) {
        submitForm()
    }
};

$(document).click(function (event) {
    var $target = $(event.target);
    if ( !$target.closest('.main-wrapper').length &&
        $('.main-wrapper').is(":visible") ) {
        CancelMenu();
    }
});

let formCountdownTimer = null;

const OpenMenu = (data) => {
    $('.main-wrapper').fadeIn(0);
    $('.background').fadeIn(0);
    SetHeader(data.header);
    addField(data.rows);

    if (data.timeout) {
        startFormCountdown(data.timeout);
    } else {
        clearInterval(formCountdownTimer);
        $('#form-countdown').remove();
    }
};

function startFormCountdown(seconds) {
    clearInterval(formCountdownTimer);
    $('#form-countdown').remove();

    let remaining = seconds;
    $('.heading').append(`<span id="form-countdown" class="form-timer">${remaining}s</span>`);

    formCountdownTimer = setInterval(() => {
        remaining--;
        if (remaining <= 0) {
            clearInterval(formCountdownTimer);
            $('#form-countdown').text('Time expired');
            CancelMenu(); // This will call NUI and close the menu
        } else {
            $('#form-countdown').text(`${remaining}s`);
        }
    }, 1000);
}


const CancelMenu = () => {
    $.post(`https://blrp_ui/cancel`)
    return CloseMenu();
}

const CloseMenu = () => {
    for (const select of selects) {
        if ( select[0] ) {
            select[0].selectize.destroy();
        }
    }

    for (const field of fields) {
        const fieldElement = $(Rows[field.id])
        fieldElement.remove();
    }

    $(`.main-wrapper`).fadeOut(0);
    $(`.background`).fadeOut(0);
    $('.body').empty();
    $(saved).remove();
    fields = [];
    selects = [];
    Rows = [];
    saved = "";
    clearInterval(formCountdownTimer);
    $('#form-countdown').remove();
};

