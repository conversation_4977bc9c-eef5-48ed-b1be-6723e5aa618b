(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[196],{

/***/ 1557:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1559);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("34d8399e", content, true, {"sourceMap":false});

/***/ }),

/***/ 1558:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tabs_vue_vue_type_style_index_0_id_08df9312_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1557);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tabs_vue_vue_type_style_index_0_id_08df9312_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tabs_vue_vue_type_style_index_0_id_08df9312_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1559:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tablet-phone .v-tabs{background-color:transparent!important;margin-top:8px!important;padding:0 4px!important}.tablet-phone .v-tabs .v-tab{background-color:transparent!important;border-radius:8px!important;margin:0 2px!important;min-width:0!important;padding:8px 6px!important;transition:all .2s ease!important}.tablet-phone .v-tabs .v-tab:before{display:none!important}.tablet-phone .v-tabs .v-tab.v-tab--active{background-color:hsla(0,0%,100%,.15)!important;color:#fff!important}.tablet-phone .v-tabs .v-tab.v-tab--active .v-icon{color:inherit!important}.tablet-phone .v-tabs .v-tab:not(.v-tab--active):hover{background-color:hsla(0,0%,100%,.08)!important}.tablet-phone .v-tabs .v-tab .v-icon{font-size:18px!important;margin:0!important}.tablet-phone .v-tabs .v-tab span{margin:0!important;padding:0!important}.tablet-phone .v-tabs .v-tabs-slider-wrapper{display:none!important}.tablet-phone .v-tabs .v-tabs-bar{background-color:transparent!important;border:none!important}.tablet-phone .v-tabs .v-tab{flex:1 1 auto!important;max-width:none!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1560:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/VMenu.js
var VMenu = __webpack_require__(461);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-tabs.vue?vue&type=template&id=08df9312










var app_tabsvue_type_template_id_08df9312_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VTabs["a" /* default */], {
    attrs: {
      "grow": ""
    }
  }, _vm._l(_vm.compiledTabs, function (tab) {
    return tab.visible && !tab.check || tab.check && tab.check() ? _c(VTab["a" /* default */], {
      key: tab.name,
      attrs: {
        "to": tab.route
      }
    }, [tab.route ? _c('span', [_c(VTooltip["a" /* default */], {
      attrs: {
        "top": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref) {
          var on = _ref.on,
            attrs = _ref.attrs;
          return [_c('span', _vm._g(_vm._b({}, 'span', attrs, false), on), [tab.icon ? _c(VIcon["a" /* default */], {
            attrs: {
              "color": tab.iconColor,
              "left": "",
              "small": ""
            }
          }, [_vm._v("\n                " + _vm._s(tab.icon) + "\n              ")]) : _vm._e(), _vm._v(" "), tab.name && _vm.mode === 'tablet' ? _c('span', {
            style: {
              color: tab.color
            }
          }, [_vm._v("\n                " + _vm._s(tab.name) + "\n              ")]) : _vm._e()], 1)];
        }
      }], null, true)
    }, [_vm._v(" "), _c('div', [_vm._v("\n            " + _vm._s(tab.name) + "\n          ")])])], 1) : _c(VMenu["a" /* default */], {
      attrs: {
        "bottom": "",
        "left": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref2) {
          var on = _ref2.on,
            attrs = _ref2.attrs;
          return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
            staticClass: "align-self-center mr-4",
            attrs: {
              "text": "",
              "large": "",
              "width": "100%"
            }
          }, 'v-btn', attrs, false), on), [_vm._v("\n          ...\n          "), _c('i', {
            staticClass: "fa-solid fa-menu-down"
          })])];
        }
      }], null, true)
    }, [_vm._v(" "), _c(VList["a" /* default */], _vm._l(tab.children, function (childTab) {
      return _c(VListItem["a" /* default */], {
        key: childTab.name,
        attrs: {
          "to": childTab.route
        }
      }, [childTab.route ? _c('span', [_vm._v("\n            " + _vm._s(childTab.name) + "\n          ")]) : _vm._e()]);
    }), 1)], 1)], 1) : _vm._e();
  }), 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-tabs.vue?vue&type=template&id=08df9312

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-tabs.vue?vue&type=script&lang=ts


















function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var app_tabsvue_type_script_lang_ts = ({
  name: 'app-tabs',
  props: ['tabs'],
  computed: _objectSpread({
    shouldShowTabNames: function shouldShowTabNames() {
      return this.mode === 'tablet';
    },
    compiledTabs: function compiledTabs() {
      var tabs = Object(toConsumableArray["a" /* default */])(this.tabs);
      var _iterator = _createForOfIteratorHelper(tabs),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var tab = _step.value;
          tab.visible = !tab.requires || this.hasAnyGroup(tab.requires) || tab.requires.length < 1;
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      return tabs;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    hasAnyGroup: 'auth/hasAnyGroup',
    currentFaction: 'auth/currentFaction',
    mode: 'system/mode'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-tabs.vue?vue&type=script&lang=ts
 /* harmony default export */ var Common_app_tabsvue_type_script_lang_ts = (app_tabsvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./components/Common/app-tabs.vue?vue&type=style&index=0&id=08df9312&prod&lang=scss
var app_tabsvue_type_style_index_0_id_08df9312_prod_lang_scss = __webpack_require__(1558);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-tabs.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_tabsvue_type_script_lang_ts,
  app_tabsvue_type_template_id_08df9312_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_tabs = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1624:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1625);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("7b5d4dc6", content, true, {"sourceMap":false});

/***/ }),

/***/ 1625:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-input--checkbox.v-input--indeterminate.v-input--is-disabled{opacity:.6}.v-input--checkbox.v-input--dense{margin-top:4px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1700:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(139);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(62);
/* harmony import */ var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(1624);
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(894);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(49);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(106);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(505);








var _excluded = ["title"];


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Styles

 // Components


 // Mixins


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_mixins_selectable__WEBPACK_IMPORTED_MODULE_14__[/* default */ "a"].extend({
  name: 'v-checkbox',
  props: {
    indeterminate: Boolean,
    indeterminateIcon: {
      type: String,
      default: '$checkboxIndeterminate'
    },
    offIcon: {
      type: String,
      default: '$checkboxOff'
    },
    onIcon: {
      type: String,
      default: '$checkboxOn'
    }
  },
  data: function data() {
    return {
      inputIndeterminate: this.indeterminate
    };
  },
  computed: {
    classes: function classes() {
      return _objectSpread(_objectSpread({}, _VInput__WEBPACK_IMPORTED_MODULE_13__[/* default */ "a"].options.computed.classes.call(this)), {}, {
        'v-input--selection-controls': true,
        'v-input--checkbox': true,
        'v-input--indeterminate': this.inputIndeterminate
      });
    },
    computedIcon: function computedIcon() {
      if (this.inputIndeterminate) {
        return this.indeterminateIcon;
      } else if (this.isActive) {
        return this.onIcon;
      } else {
        return this.offIcon;
      }
    },
    // Do not return undefined if disabled,
    // according to spec, should still show
    // a color when disabled and active
    validationState: function validationState() {
      if (this.isDisabled && !this.inputIndeterminate) return undefined;
      if (this.hasError && this.shouldValidate) return 'error';
      if (this.hasSuccess) return 'success';
      if (this.hasColor !== null) return this.computedColor;
      return undefined;
    }
  },
  watch: {
    indeterminate: function indeterminate(val) {
      var _this = this;
      // https://github.com/vuetifyjs/vuetify/issues/8270
      this.$nextTick(function () {
        return _this.inputIndeterminate = val;
      });
    },
    inputIndeterminate: function inputIndeterminate(val) {
      this.$emit('update:indeterminate', val);
    },
    isActive: function isActive() {
      if (!this.indeterminate) return;
      this.inputIndeterminate = false;
    }
  },
  methods: {
    genCheckbox: function genCheckbox() {
      var _this$attrs$ = this.attrs$,
        title = _this$attrs$.title,
        checkboxAttrs = Object(_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"])(_this$attrs$, _excluded);
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_12__[/* default */ "a"], this.setTextColor(this.validationState, {
        props: {
          dense: this.dense,
          dark: this.dark,
          light: this.light
        }
      }), this.computedIcon), this.genInput('checkbox', _objectSpread(_objectSpread({}, checkboxAttrs), {}, {
        'aria-checked': this.inputIndeterminate ? 'mixed' : this.isActive.toString()
      })), this.genRipple(this.setTextColor(this.rippleState))]);
    },
    genDefaultSlot: function genDefaultSlot() {
      return [this.genCheckbox(), this.genLabel()];
    }
  }
}));

/***/ }),

/***/ 2182:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCheckbox/VCheckbox.js
var VCheckbox = __webpack_require__(1700);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social.vue?vue&type=template&id=f0c71d2a&scoped=true














var socialvue_type_template_id_f0c71d2a_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('div', [_c('app-tabs', {
    attrs: {
      "tabs": _vm.tabs
    }
  }), _vm._v(" "), _vm.user.level > 0 ? _c('NuxtChild', {
    key: _vm.$route.fullPath
  }) : _c('div', {
    staticClass: "p-2"
  }, [_c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.submit.apply(null, arguments);
      }
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["c" /* VCardTitle */], [_c(VIcon["a" /* default */], {
    attrs: {
      "right": "",
      "color": "red",
      "small": ""
    }
  }, [_vm._v("\n            fa-solid fa-l\n          ")]), _vm._v(" "), _c('span', {
    staticClass: "ml-4"
  }, [_vm._v("\n            Account Registration\n          ")])], 1), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('span', [_c(VAlert["a" /* default */], {
    attrs: {
      "color": "primary"
    }
  }, [_vm._v("\n              Your device has not yet been registered with LifeInvader. Please fill out the following fields.\n              If you have already filled these fields out, you can simply tap the register button.\n            ")])], 1), _vm._v(" "), _c(VRow["a" /* default */], [_vm.mode === 'tablet' ? _c(VCol["a" /* default */], {
    attrs: {
      "cols": "4"
    }
  }, [_c(VImg["a" /* default */], {
    attrs: {
      "src": "https://wiki.glife.fr/uploads/images/gallery/2022-01/image-*************.png"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.submit.apply(null, arguments);
      }
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "hint": "Think hard! Choose a name that best fits you. If you want, even put your name. We definitely don't care. This name can only be changed every 15 days.",
      "label": "Display Name"
    },
    model: {
      value: _vm.form.display_name,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "display_name", $$v);
      },
      expression: "form.display_name"
    }
  }), _vm._v(" "), _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("\n                  ⚙ Life Invader settings can be changed in the future, in your device settings.\n                ")]), _vm._v(" "), _c('div', {
    staticClass: "mt-3"
  }, [_c('span', {
    staticClass: "text-h7"
  }, [_vm._v("\n                    Policy Agreement\n                  ")]), _vm._v(" "), _c(VCheckbox["a" /* default */], {
    attrs: {
      "hide-details": "",
      "dense": "",
      "label": "I agree to LifeInvaders ToS and Privacy Policy"
    },
    model: {
      value: _vm.form.agree_policy,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "agree_policy", $$v);
      },
      expression: "form.agree_policy"
    }
  }), _vm._v(" "), _c(VCheckbox["a" /* default */], {
    attrs: {
      "hide-details": "",
      "dense": "",
      "label": "I understand the risk associated with LifeInvader "
    },
    model: {
      value: _vm.form.agree_risks,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "agree_risks", $$v);
      },
      expression: "form.agree_risks"
    }
  })], 1), _vm._v(" "), _c(VAlert["a" /* default */], {
    staticClass: "mt-2",
    attrs: {
      "color": "secondary"
    }
  }, [_vm._v("\n                  💡 Even though only one account per biological person is possible, LifeInvader offers features allowing you to post as a verified business, crew, or just yourself.\n                ")])], 1)], 1)], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    attrs: {
      "block": "",
      "color": "red"
    },
    on: {
      "click": _vm.submit
    }
  }, [_vm._v("\n            Register\n          ")])], 1)], 1)], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/social.vue?vue&type=template&id=f0c71d2a&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./components/Common/app-tabs.vue + 4 modules
var app_tabs = __webpack_require__(1560);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social.vue?vue&type=script&lang=ts









function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/* harmony default export */ var socialvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  components: {
    AppTabs: app_tabs["a" /* default */]
  },
  data: function data() {
    return {
      tabs: [{
        name: 'Home',
        route: '/social',
        iconColor: 'red',
        icon: 'fa-solid fa-l',
        requires: []
      }, {
        name: 'Submit',
        iconColor: 'red',
        icon: 'fa-solid fa-plus',
        route: '/social/submit',
        requires: []
      }, {
        name: 'Topics',
        iconColor: 'red',
        icon: 'fa-solid fa-bars',
        route: '/social/topics',
        requires: ['admin']
      },
      // {
      //   name: 'Events',
      //   iconColor: 'red',
      //   icon: 'fa-regular fa-calendar-star',
      //   route: '/social/events',
      //   requires: [],
      // },
      {
        name: 'Invaders',
        iconColor: 'red',
        icon: 'fa-solid fa-person',
        route: '/social/people',
        requires: []
      }, {
        name: 'Crews',
        iconColor: 'red',
        icon: 'fa-solid fa-user-group',
        route: '/social/groups',
        requires: []
      }, {
        name: 'Leaderboards',
        iconColor: 'red',
        icon: 'fa-solid fa-trophy',
        route: '/social/leaderboards',
        requires: []
      },
      // {
      //   name: 'Friend Requests',
      //   iconColor: 'red',
      //   icon: 'fa-solid fa-person-circle-plus',
      //   route: '/social/friend-requests',
      //   requires: [],
      // },
      {
        name: 'Settings',
        iconColor: 'red',
        icon: 'fa-solid fa-screwdriver-wrench',
        route: '/social/settings',
        requires: []
      }],
      form: {
        display_name: null,
        avatar_url: null,
        color: null,
        agree_policy: false,
        agree_risks: false
      }
    };
  },
  mounted: function mounted() {
    if (this.user.display_name !== '@no display name set') {
      this.form.display_name = this.user.display_name;
    }
    this.form.avatar_url = this.user.avatar_url;
  },
  methods: {
    submit: function submit() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        var res;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$post('/persons/me/install', _this.form).catch(function () {});
            case 2:
              res = _context.sent;
              if (!res) {
                _context.next = 6;
                break;
              }
              _context.next = 6;
              return _this.$store.dispatch('auth/fetchUser');
            case 6:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    user: 'auth/user'
  }))
}));
// CONCATENATED MODULE: ./pages/social.vue?vue&type=script&lang=ts
 /* harmony default export */ var pages_socialvue_type_script_lang_ts = (socialvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/social.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_socialvue_type_script_lang_ts,
  socialvue_type_template_id_f0c71d2a_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "f0c71d2a",
  null
  
)

/* harmony default export */ var social = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);