(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[149],{

/***/ 1720:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1847);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("1744d8fb", content, true, {"sourceMap":false});

/***/ }),

/***/ 1846:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_ea48792a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1720);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_ea48792a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_ea48792a_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1847:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".crew-chat-container[data-v-ea48792a]{background-color:#000;min-height:100vh}.crew-chat-card[data-v-ea48792a]{background-color:#27272a!important;border-left:4px solid transparent;cursor:pointer;transition:all .2s ease-in-out}.crew-chat-card[data-v-ea48792a]:hover{background-color:#3f3f46!important;border-left-color:#71717a;box-shadow:0 4px 12px rgba(0,0,0,.25)!important;transform:translateY(-2px)}.action-btn[data-v-ea48792a]{color:#a1a1aa;opacity:.8;transition:all .2s ease}.action-btn[data-v-ea48792a]:hover{background-color:#3f3f46;color:#f4f4f5;opacity:1}.last-message-container[data-v-ea48792a]{align-items:flex-start;display:flex;min-height:50px}.last-message-bubble[data-v-ea48792a]{background-color:#18181b;border:1px solid #3f3f46;border-radius:12px;padding:10px 14px;transition:background-color .2s ease;width:100%}.last-message-bubble[data-v-ea48792a]:hover{background-color:#27272a;border-color:#52525b}.no-messages-bubble[data-v-ea48792a]{align-items:center;background-color:#18181b;border:1px dashed #52525b;border-radius:12px;color:#a1a1aa;display:flex;font-style:italic;padding:10px 14px;width:100%}.message-content[data-v-ea48792a]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;line-height:1.4;max-height:2.8em;overflow:hidden}.message-content .app-markdown-view[data-v-ea48792a]{display:inline}.sender-name[data-v-ea48792a]{color:#f4f4f5;font-weight:600}.message-text[data-v-ea48792a]{color:#e4e4e7}.message-time[data-v-ea48792a]{color:#a1a1aa;font-size:12px;line-height:1.2}.flex-nowrap[data-v-ea48792a]{flex-wrap:nowrap!important}.flex-shrink-0[data-v-ea48792a]{flex-shrink:0!important}.flex-shrink-1[data-v-ea48792a]{flex-shrink:1!important}.min-width-0[data-v-ea48792a]{min-width:0!important}.v-chip.primary[data-v-ea48792a]{background:linear-gradient(45deg,#ff6b6b,#ee5a24)!important}.v-chip.secondary[data-v-ea48792a]{background:linear-gradient(45deg,#74b9ff,#0984e3)!important}.theme--dark .crew-chat-card[data-v-ea48792a]{background-color:#18181b!important}.theme--dark .crew-chat-card[data-v-ea48792a]:hover{background-color:#27272a!important;border-left-color:#71717a}.theme--dark .last-message-bubble[data-v-ea48792a]{background-color:#09090b;border-color:#27272a}.theme--dark .last-message-bubble[data-v-ea48792a]:hover{background-color:#18181b;border-color:#3f3f46}.theme--dark .no-messages-bubble[data-v-ea48792a]{background-color:#09090b;border-color:#3f3f46;color:#71717a}.theme--dark .action-btn[data-v-ea48792a]{color:#71717a}.theme--dark .action-btn[data-v-ea48792a]:hover{background-color:#27272a;color:#e4e4e7}.empty-state[data-v-ea48792a]{background-color:#27272a!important;border:1px dashed #52525b}.empty-state .v-icon[data-v-ea48792a]{color:#71717a}.empty-state h3[data-v-ea48792a]{color:#e4e4e7!important}.empty-state p[data-v-ea48792a]{color:#a1a1aa!important}@media(max-width:600px){.crew-chat-card .v-card-text[data-v-ea48792a]{padding:12px!important}.crew-chat-card .text-h6[data-v-ea48792a]{font-size:1.1rem!important}.empty-state .v-card-text[data-v-ea48792a]{padding:24px 16px!important}.empty-state .v-icon[data-v-ea48792a]{font-size:48px!important}.empty-state .d-flex[data-v-ea48792a]{flex-direction:column!important}.empty-state .d-flex .v-btn[data-v-ea48792a]{margin-bottom:8px;margin-right:0!important;width:100%}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2150:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/VChip.js
var VChip = __webpack_require__(467);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/index.vue?vue&type=template&id=ea48792a&scoped=true












var crew_chatvue_type_template_id_ea48792a_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "ma-1 crew-chat-container"
  }, [_c('div', [_c('div', {
    staticClass: "pa-3"
  }, [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "cols": "6"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary",
      "block": ""
    },
    on: {
      "click": function click($event) {
        _vm.showCreateDialog = true;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-plus")]), _vm._v("\n            Create Crew Chat\n          ")], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "cols": "6"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "secondary",
      "block": ""
    },
    on: {
      "click": function click($event) {
        _vm.showJoinDialog = true;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-key")]), _vm._v("\n            Join by Code\n          ")], 1)], 1)], 1)], 1)]), _vm._v(" "), _vm._l(_vm.sortedChats, function (chat) {
    return _c(VCard["a" /* default */], {
      key: chat.id,
      staticClass: "mt-3 crew-chat-card",
      attrs: {
        "hover": "",
        "elevation": "2"
      },
      on: {
        "click": function click($event) {
          return _vm.goToChat(chat);
        }
      }
    }, [_c(components_VCard["b" /* VCardText */], {
      staticClass: "pa-4"
    }, [_c(VRow["a" /* default */], {
      attrs: {
        "no-gutters": ""
      }
    }, [_c(VCol["a" /* default */], {
      staticClass: "d-flex flex-column"
    }, [_c('div', {
      staticClass: "d-flex justify-space-between align-center mb-2"
    }, [_c('div', {
      staticClass: "d-flex align-center"
    }, [_c('h3', {
      staticClass: "text-h6 mb-0 mr-2"
    }, [_vm._v(_vm._s(chat.name))]), _vm._v(" "), chat.group && chat.group.owner_person_id === _vm.user.id ? _c(VChip["a" /* default */], {
      staticClass: "mr-2",
      attrs: {
        "x-small": "",
        "color": "primary",
        "text-color": "white"
      }
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "x-small": "",
        "left": ""
      }
    }, [_vm._v("fa-solid fa-crown")]), _vm._v("\n                Owner\n              ")], 1) : chat.can_manage ? _c(VChip["a" /* default */], {
      staticClass: "mr-2",
      attrs: {
        "x-small": "",
        "color": "secondary",
        "text-color": "white"
      }
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "x-small": "",
        "left": ""
      }
    }, [_vm._v("fa-solid fa-user-shield")]), _vm._v("\n                Manager\n              ")], 1) : _vm._e()], 1), _vm._v(" "), chat.can_manage ? _c('div', [_c(VTooltip["a" /* default */], {
      attrs: {
        "bottom": ""
      },
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref) {
          var on = _ref.on,
            attrs = _ref.attrs;
          return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
            staticClass: "action-btn",
            attrs: {
              "icon": "",
              "small": ""
            },
            on: {
              "click": function click($event) {
                $event.stopPropagation();
                return _vm.openShareCodeDialog(chat);
              }
            }
          }, 'v-btn', attrs, false), on), [_c(VIcon["a" /* default */], {
            attrs: {
              "small": ""
            }
          }, [_vm._v("fa-solid fa-share")])], 1)];
        }
      }], null, true)
    }, [_vm._v(" "), _c('span', [_vm._v("Manage Share Code")])])], 1) : _vm._e()]), _vm._v(" "), _c('div', {
      staticClass: "last-message-container mb-3"
    }, [chat.last_message && chat.last_message[0] ? _c('div', {
      staticClass: "last-message-bubble"
    }, [_c('div', {
      staticClass: "message-content text-body-2 mb-1"
    }, [_c('span', {
      staticClass: "font-weight-medium sender-name"
    }, [_vm._v(_vm._s(chat.last_message[0].person.display_name) + ":")]), _vm._v(" "), _c('span', {
      staticClass: "ml-1 message-text mt-2"
    }, [_c('app-markdown-view', {
      attrs: {
        "source": chat.last_message[0].message
      }
    })], 1)]), _vm._v(" "), _c('div', {
      staticClass: "message-time text-caption"
    }, [_c('app-timestamp', {
      attrs: {
        "stamp": chat.last_message[0].created_at
      }
    }), _vm._v(" ago\n              ")], 1)]) : _c('div', {
      staticClass: "no-messages-bubble"
    }, [_c(VIcon["a" /* default */], {
      staticClass: "mr-3",
      attrs: {
        "small": ""
      }
    }, [_vm._v("fa-regular fa-comment")]), _vm._v("\n              No messages yet\n            ")], 1)])])], 1)], 1)], 1);
  }), _vm._v(" "), _vm.chats.length === 0 ? _c(VCard["a" /* default */], {
    staticClass: "mt-4 text-center empty-state"
  }, [_c(components_VCard["b" /* VCardText */], {
    staticClass: "pa-8"
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mb-4",
    attrs: {
      "size": "64",
      "color": "grey lighten-1"
    }
  }, [_vm._v("fa-regular fa-comments")]), _vm._v(" "), _c('h3', {
    staticClass: "text-h5 mb-3 grey--text"
  }, [_vm._v("No Crew Chats Yet")]), _vm._v(" "), _c('p', {
    staticClass: "text-body-1 grey--text text--darken-1 mb-4"
  }, [_vm._v("\n        Get started by creating your first crew chat or joining an existing one.\n      ")]), _vm._v(" "), _c('div', {
    staticClass: "d-flex flex-column flex-sm-row justify-center align-center"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "mb-2 mb-sm-0 mr-sm-3",
    attrs: {
      "color": "primary",
      "large": ""
    },
    on: {
      "click": function click($event) {
        _vm.showCreateDialog = true;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "left": ""
    }
  }, [_vm._v("fa-solid fa-plus")]), _vm._v("\n          Create Crew Chat\n        ")], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "secondary",
      "large": "",
      "outlined": ""
    },
    on: {
      "click": function click($event) {
        _vm.showJoinDialog = true;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "left": ""
    }
  }, [_vm._v("fa-solid fa-key")]), _vm._v("\n          Join by Code\n        ")], 1)], 1)], 1)], 1) : _vm._e(), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Create Crew Chat"
    },
    model: {
      value: _vm.showCreateDialog,
      callback: function callback($$v) {
        _vm.showCreateDialog = $$v;
      },
      expression: "showCreateDialog"
    }
  }, [_c('div', {
    staticClass: "pa-2"
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "label": "Chat Name",
      "rules": [function (v) {
        return !!v || 'Name is required';
      }, function (v) {
        return v.length >= 3 || 'Name must be at least 3 characters';
      }],
      "counter": "45",
      "maxlength": "45",
      "solo": ""
    },
    model: {
      value: _vm.createForm.name,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "name", $$v);
      },
      expression: "createForm.name"
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "mt-4"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary",
      "loading": _vm.creating,
      "block": ""
    },
    on: {
      "click": _vm.createChat
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-plus")]), _vm._v("\n          Create Crew Chat\n        ")], 1)], 1)], 1)]), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Join by Share Code"
    },
    model: {
      value: _vm.showJoinDialog,
      callback: function callback($$v) {
        _vm.showJoinDialog = $$v;
      },
      expression: "showJoinDialog"
    }
  }, [_c('div', {
    staticClass: "pa-2"
  }, [_c(VTextField["a" /* default */], {
    staticStyle: {
      "text-transform": "uppercase"
    },
    attrs: {
      "label": "Share Code",
      "rules": [function (v) {
        return !!v || 'Share code is required';
      }],
      "maxlength": "5",
      "solo": "",
      "hint": "Enter the 5-character code (##AA#) shared by the crew owner",
      "placeholder": "12AB3"
    },
    on: {
      "input": function input($event) {
        _vm.joinForm.share_code = $event.toUpperCase();
      }
    },
    model: {
      value: _vm.joinForm.share_code,
      callback: function callback($$v) {
        _vm.$set(_vm.joinForm, "share_code", $$v);
      },
      expression: "joinForm.share_code"
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "mt-4"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary",
      "loading": _vm.joining,
      "block": ""
    },
    on: {
      "click": _vm.joinByCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-key")]), _vm._v("\n          Join Crew Chat\n        ")], 1)], 1)], 1)]), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Manage Share Code"
    },
    model: {
      value: _vm.showShareCodeDialog,
      callback: function callback($$v) {
        _vm.showShareCodeDialog = $$v;
      },
      expression: "showShareCodeDialog"
    }
  }, [_vm.selectedChat ? _c('div', [_c('div', {
    staticClass: "text-h6 mb-3"
  }, [_vm._v(_vm._s(_vm.selectedChat.name))]), _vm._v(" "), _vm.selectedChat.share_code ? _c('div', [_c(VAlert["a" /* default */], {
    staticClass: "mb-3",
    attrs: {
      "type": "info"
    }
  }, [_vm._v("\n          Share this code with others to let them join your crew chat:\n        ")]), _vm._v(" "), !_vm.showShareCode ? _c('div', [_c(VBtn["a" /* default */], {
    staticClass: "mb-3",
    attrs: {
      "color": "primary",
      "block": ""
    },
    on: {
      "click": function click($event) {
        _vm.showShareCode = true;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-eye")]), _vm._v("\n            Show Share Code\n          ")], 1)], 1) : _c('div', [_c(VTextField["a" /* default */], {
    attrs: {
      "value": _vm.selectedChat.share_code,
      "label": "Share Code",
      "readonly": ""
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-2 mb-3",
    attrs: {
      "color": "secondary",
      "block": ""
    },
    on: {
      "click": function click($event) {
        _vm.showShareCode = false;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-eye-slash")]), _vm._v("\n            Hide Share Code\n          ")], 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "mt-4"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "mb-2",
    attrs: {
      "color": "warning",
      "loading": _vm.refreshing,
      "block": ""
    },
    on: {
      "click": _vm.refreshShareCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-refresh")]), _vm._v("\n            Refresh Code\n          ")], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "error",
      "loading": _vm.resetting,
      "block": ""
    },
    on: {
      "click": _vm.resetShareCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-trash")]), _vm._v("\n            Remove Code\n          ")], 1)], 1)], 1) : _c('div', [_c(VAlert["a" /* default */], {
    staticClass: "mb-3",
    attrs: {
      "type": "warning"
    }
  }, [_vm._v("\n          No share code generated yet. Generate one to allow others to join.\n        ")]), _vm._v(" "), _c('div', {
    staticClass: "mt-4"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary",
      "loading": _vm.generating,
      "block": ""
    },
    on: {
      "click": _vm.generateShareCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-plus")]), _vm._v("\n            Generate Share Code\n          ")], 1)], 1)], 1)]) : _vm._e()])], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/crew-chat/index.vue?vue&type=template&id=ea48792a&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.sort.js
var es_array_sort = __webpack_require__(273);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/index.vue?vue&type=script&lang=js



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }















/* harmony default export */ var crew_chatvue_type_script_lang_js = ({
  components: {
    AppTimestamp: app_timestamp["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */]
  },
  data: function data() {
    return {
      chats: [],
      showCreateDialog: false,
      showJoinDialog: false,
      showShareCodeDialog: false,
      selectedChat: null,
      showShareCode: false,
      creating: false,
      joining: false,
      generating: false,
      refreshing: false,
      resetting: false,
      createForm: {
        name: '',
        description: '',
        logo_url: '',
        is_private: false
      },
      joinForm: {
        share_code: ''
      }
    };
  },
  mounted: function mounted() {
    this.index();
  },
  methods: {
    index: function index() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$get('/social/chat/my-chats');
            case 2:
              _this.chats = _context.sent;
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    goToChat: function goToChat(chat) {
      this.$router.push({
        path: "/crew-chat/".concat(chat.id)
      });
    },
    createChat: function createChat() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var response, _error$response;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (!(!_this2.createForm.name || _this2.createForm.name.length < 3)) {
                _context2.next = 3;
                break;
              }
              _this2.$toast.error('Name must be at least 3 characters');
              return _context2.abrupt("return");
            case 3:
              _this2.creating = true;
              _context2.prev = 4;
              _context2.next = 7;
              return _this2.$axios.$post('/social/chat/create', _this2.createForm);
            case 7:
              response = _context2.sent;
              _this2.showCreateDialog = false;
              _this2.createForm = {
                name: '',
                description: '',
                logo_url: '',
                is_private: false
              };
              _context2.next = 12;
              return _this2.index();
            case 12:
              _this2.$toast.success('Crew chat created successfully!');
              _context2.next = 18;
              break;
            case 15:
              _context2.prev = 15;
              _context2.t0 = _context2["catch"](4);
              _this2.$toast.error(((_error$response = _context2.t0.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.message) || 'Failed to create crew chat');
            case 18:
              _this2.creating = false;
            case 19:
            case "end":
              return _context2.stop();
          }
        }, _callee2, null, [[4, 15]]);
      }))();
    },
    joinByCode: function joinByCode() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var _error$response2;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (_this3.joinForm.share_code) {
                _context3.next = 3;
                break;
              }
              _this3.$toast.error('Share code is required');
              return _context3.abrupt("return");
            case 3:
              _this3.joining = true;
              _context3.prev = 4;
              _context3.next = 7;
              return _this3.$axios.$post('/social/chat/join-by-code', _this3.joinForm);
            case 7:
              _this3.showJoinDialog = false;
              _this3.joinForm = {
                shareCode: ''
              };
              _context3.next = 11;
              return _this3.index();
            case 11:
              _this3.$toast.success('Successfully joined crew chat!');
              _context3.next = 17;
              break;
            case 14:
              _context3.prev = 14;
              _context3.t0 = _context3["catch"](4);
              _this3.$toast.error(((_error$response2 = _context3.t0.response) === null || _error$response2 === void 0 || (_error$response2 = _error$response2.data) === null || _error$response2 === void 0 ? void 0 : _error$response2.message) || 'Failed to join crew chat');
            case 17:
              _this3.joining = false;
            case 18:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[4, 14]]);
      }))();
    },
    openShareCodeDialog: function openShareCodeDialog(chat) {
      this.selectedChat = chat;
      this.showShareCode = false; // Reset share code visibility
      this.showShareCodeDialog = true;
    },
    generateShareCode: function generateShareCode() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var response, _error$response3;
        return regeneratorRuntime.wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _this4.generating = true;
              _context4.prev = 1;
              _context4.next = 4;
              return _this4.$axios.$post("/social/chat/generate-share-code/".concat(_this4.selectedChat.id));
            case 4:
              response = _context4.sent;
              // Use Vue.set to ensure reactivity
              _this4.$set(_this4.selectedChat, 'share_code', response.share_code);
              _this4.$toast.success('Share code generated!');
              _context4.next = 12;
              break;
            case 9:
              _context4.prev = 9;
              _context4.t0 = _context4["catch"](1);
              _this4.$toast.error(((_error$response3 = _context4.t0.response) === null || _error$response3 === void 0 || (_error$response3 = _error$response3.data) === null || _error$response3 === void 0 ? void 0 : _error$response3.message) || 'Failed to generate share code');
            case 12:
              _this4.generating = false;
            case 13:
            case "end":
              return _context4.stop();
          }
        }, _callee4, null, [[1, 9]]);
      }))();
    },
    refreshShareCode: function refreshShareCode() {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee5() {
        var response, _error$response4;
        return regeneratorRuntime.wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _this5.refreshing = true;
              _context5.prev = 1;
              _context5.next = 4;
              return _this5.$axios.$post("/social/chat/refresh-share-code/".concat(_this5.selectedChat.id));
            case 4:
              response = _context5.sent;
              // Use Vue.set to ensure reactivity
              _this5.$set(_this5.selectedChat, 'share_code', response.share_code);
              _this5.$toast.success('Share code refreshed!');
              _context5.next = 12;
              break;
            case 9:
              _context5.prev = 9;
              _context5.t0 = _context5["catch"](1);
              _this5.$toast.error(((_error$response4 = _context5.t0.response) === null || _error$response4 === void 0 || (_error$response4 = _error$response4.data) === null || _error$response4 === void 0 ? void 0 : _error$response4.message) || 'Failed to refresh share code');
            case 12:
              _this5.refreshing = false;
            case 13:
            case "end":
              return _context5.stop();
          }
        }, _callee5, null, [[1, 9]]);
      }))();
    },
    resetShareCode: function resetShareCode() {
      var _this6 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee6() {
        var _error$response5;
        return regeneratorRuntime.wrap(function _callee6$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              _this6.resetting = true;
              _context6.prev = 1;
              _context6.next = 4;
              return _this6.$axios.$post("/social/chat/reset-share-code/".concat(_this6.selectedChat.id));
            case 4:
              // Use Vue.set to ensure reactivity
              _this6.$set(_this6.selectedChat, 'share_code', null);
              _this6.$toast.success('Share code removed!');
              _context6.next = 11;
              break;
            case 8:
              _context6.prev = 8;
              _context6.t0 = _context6["catch"](1);
              _this6.$toast.error(((_error$response5 = _context6.t0.response) === null || _error$response5 === void 0 || (_error$response5 = _error$response5.data) === null || _error$response5 === void 0 ? void 0 : _error$response5.message) || 'Failed to reset share code');
            case 11:
              _this6.resetting = false;
            case 12:
            case "end":
              return _context6.stop();
          }
        }, _callee6, null, [[1, 8]]);
      }))();
    }
  },
  computed: _objectSpread(_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    user: 'auth/user'
  })), {}, {
    sortedChats: function sortedChats() {
      if (!this.chats || this.chats.length === 0) return [];
      return Object(toConsumableArray["a" /* default */])(this.chats).sort(function (a, b) {
        // Get last message timestamps
        var aLastMessage = a.last_message && a.last_message[0] ? a.last_message[0].created_at : null;
        var bLastMessage = b.last_message && b.last_message[0] ? b.last_message[0].created_at : null;

        // If both have messages, sort by most recent
        if (aLastMessage && bLastMessage) {
          return new Date(bLastMessage) - new Date(aLastMessage);
        }

        // If only one has messages, prioritize the one with messages
        if (aLastMessage && !bLastMessage) return -1;
        if (!aLastMessage && bLastMessage) return 1;

        // If neither has messages, sort alphabetically by name
        return a.name.localeCompare(b.name);
      });
    }
  })
});
// CONCATENATED MODULE: ./pages/crew-chat/index.vue?vue&type=script&lang=js
 /* harmony default export */ var pages_crew_chatvue_type_script_lang_js = (crew_chatvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/crew-chat/index.vue?vue&type=style&index=0&id=ea48792a&prod&lang=scss&scoped=true
var crew_chatvue_type_style_index_0_id_ea48792a_prod_lang_scss_scoped_true = __webpack_require__(1846);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/crew-chat/index.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_crew_chatvue_type_script_lang_js,
  crew_chatvue_type_template_id_ea48792a_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "ea48792a",
  null
  
)

/* harmony default export */ var crew_chat = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);