(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[141],{

/***/ 2144:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/clock.vue?vue&type=template&id=30ab19b8

var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('div', {
    staticClass: "p-3"
  }, [_c('app-details-box', {
    attrs: {
      "icon": "fa-solid fa-clock",
      "title": "".concat(_vm.currentGameTime, ":00"),
      "content": "Los Santos",
      "color": "primary"
    }
  }), _vm._v(" "), _vm._l(_vm.timezones, function (zone) {
    return _c('app-details-box', {
      staticClass: "mt-4",
      attrs: {
        "index": zone.name,
        "icon": zone.icon,
        "Current": "",
        "time": "",
        "title": "".concat(_vm.getTimeInTimezone(zone.timezone)),
        "content": "".concat(zone.name),
        "color": "white"
      }
    });
  })], 2)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/clock.vue?vue&type=template&id=30ab19b8

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/moment/moment.js
var moment = __webpack_require__(17);
var moment_default = /*#__PURE__*/__webpack_require__.n(moment);

// EXTERNAL MODULE: ./components/Common/app-details-box.vue + 4 modules
var app_details_box = __webpack_require__(318);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/clock.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var clockvue_type_script_lang_js = ({
  name: 'clock',
  components: {
    AppDetailsBox: app_details_box["a" /* default */]
  },
  props: [],
  data: function data() {
    return {
      checkingTimezone: null,
      timezones: [{
        name: 'Eastern (New York)',
        timezone: 'America/New_York',
        icon: 'fa-solid fa-clock'
      }, {
        name: 'America (Toronto)',
        timezone: 'America/Toronto',
        icon: 'fa-solid fa-clock'
      }, {
        name: 'Europe (London)',
        timezone: 'Europe/London',
        icon: 'fa-solid fa-clock'
      }, {
        name: 'Australia (Sydney)',
        timezone: 'Australia/Sydney',
        icon: 'fa-solid fa-clock'
      }]
    };
  },
  created: function created() {},
  methods: {
    getTimeZoneOffset: function getTimeZoneOffset(date, timeZone) {},
    getTimeDifferenceFromNow: function getTimeDifferenceFromNow(timezone) {
      var localNow = moment_default.a.tz(moment_default.a.tz.guess()).parseZone();
      var timezoneNow = moment_default.a.tz(timezone);
      return timezoneNow.diff(localNow);
      // const nowMeTimezone = moment.tz('America/New_York')
      // const nowBadlandsTimezone = moment.tz(timezone)
      //
      // return nowBadlandsTimezone.diff(nowMeTimezone)
    },
    getTimeInTimezone: function getTimeInTimezone(timezone) {
      return moment_default()().tz(timezone).format('HH:mm:ss');
    },
    getDaylightIconForTimezone: function getDaylightIconForTimezone(timezone) {
      return moment_default()().tz(timezone).format('HH:mm:ss' + '');
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    currentGameTime: "system/currentGameTime"
  }))
});
// CONCATENATED MODULE: ./pages/clock.vue?vue&type=script&lang=js
 /* harmony default export */ var pages_clockvue_type_script_lang_js = (clockvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/clock.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_clockvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var clock = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);